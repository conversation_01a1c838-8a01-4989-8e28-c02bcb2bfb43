import {LeftNavigation, LeftNavigationMenuItem} from '@panda-design/router';
import {useMatch, useParams} from 'react-router-dom';
import {css} from '@emotion/css';
import {message} from '@panda-design/components';
import {useMemo} from 'react';
import {IconLogo} from '@/icons/ievalueSidebar';
import {MCPSpaceLink, MCPSquareLink} from '@/links/mcp';
import {useMCPSpaceList} from '@/regions/mcp/mcpSpace';
import {IconLightMySpcae, IconLightPlayground, IconMySpcae, IconPlayground} from '@/icons/mcp';

const spaceCss = css`
    div:nth-child(1) {
        display: none;
    }

    div:nth-child(2) {
        left: 40px !important;
        width: calc(100% - 40px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
`;

export const useMCPItems = () => {
    const {mcpId} = useParams();
    const matchMcpSquare = useMatch('/comatestack/mcp/square');
    const matchMcpSpace = useMatch('/comatestack/mcp/space/*');
    const mcpSpaceList = useMCPSpaceList({mine: true});
    const isPlaygroundActive = Boolean(matchMcpSquare) || !!mcpId;
    const menus: LeftNavigationMenuItem[] = useMemo(
        () => {
            return [
                {
                    title: 'MCP广场',
                    shortTitle: 'MCP广场',
                    to: MCPSquareLink.toUrl(),
                    icon: isPlaygroundActive ? <IconLightPlayground /> : <IconPlayground />,
                    isActive: isPlaygroundActive,
                },
                {
                    title: '我的空间',
                    shortTitle: '我的空间',
                    icon: matchMcpSpace ? <IconLightMySpcae /> : <IconMySpcae />,
                    onClick: () => {
                        if (!mcpSpaceList?.length) {
                            message.warning('请先创建空间');
                        }
                    },
                    children: mcpSpaceList?.map(item => {
                        return {
                            title: item.name,
                            shortTitle: item.name,
                            tooltip: item.name,
                            to: MCPSpaceLink.toUrl({workspaceId: item.id}),
                            icon: <></>,
                            className: spaceCss,
                        };
                    }),
                },
            ];
        },
        [isPlaygroundActive, mcpSpaceList, matchMcpSpace]
    );
    return menus;
};

export const MCPLeftNav = () => {
    const items: LeftNavigationMenuItem[] = useMCPItems();
    const serverEdit = useMatch('/comatestack/mcp/space/:spaceId/server/:id/edit');
    if (serverEdit) {
        return null;
    }

    return (
        <LeftNavigation
            enableCollapse={false}
            style={{gap: 0, zIndex: 100, background: '#FFF'}}
            logo={{
                icon: <IconLogo />,
                title: 'AI Tools',
                to: MCPSquareLink.toUrl(),
            }}
            items={items}
        />
    );
};
