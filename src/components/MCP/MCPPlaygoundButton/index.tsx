import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {MCPPlaygroundLink} from '@/links/mcp';

const StyledButton = styled(Button)`
    border: 1px solid #0080FF !important;
    color: #0080FF !important;
`;

export const MCPPlaygoundButton = () => {
    const handleClick = () => {
        window.open(MCPPlaygroundLink.toUrl({}), '_blank', 'noreferrer');
    };

    return (
        <StyledButton onClick={handleClick}>MCP Playground</StyledButton>
    );
};
