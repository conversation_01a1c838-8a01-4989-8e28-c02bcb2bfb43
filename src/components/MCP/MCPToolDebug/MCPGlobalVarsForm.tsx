import {Empty, Form} from 'antd';
import {useEffect} from 'react';
import {useMCPGlobalVarsContext} from './ Providers/MCPServerConfigProvider';
import {MCPParamsDynamicFormItem} from './MCPParamsDynamicFormItem';

export const MCPGlobalVarsForm = () => {
    const {globalVarsFormInstance, globalVars, initGlobalVars} = useMCPGlobalVarsContext();
    useEffect(
        () => {
            initGlobalVars();
        },
        [initGlobalVars]
    );
    return (
        <Form form={globalVarsFormInstance}>
            {
                globalVars.length > 0 ? globalVars.map(item => (
                    <MCPParamsDynamicFormItem
                        key={item.name}
                        name={item.name}
                        label={item.name}
                        description={item.description}
                        type={item.dataType}
                        required={item.required}
                    />
                )) : <Empty />
            }
        </Form>
    );
};
