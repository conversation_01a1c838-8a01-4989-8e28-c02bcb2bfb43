import {Button} from '@panda-design/components';
import {useCallback} from 'react';
import {useMCPToolDebugContext} from './ Providers/MCPToolDebugProvider';

interface Props {
    onlyDebug?: boolean;
    topHandler?: () => void;
}

export function MCPToolDebugButton({topHandler}: Props) {
    const {
        runDebug,
        debugLoading,
    } = useMCPToolDebugContext();
    // useMCPToolDebugContext提供的runDebug会返回一些信息，
    // 如果外部需要这种信息，可以传入topHandler，在topHandler中自行调用runDebug等方法。
    const handleDebug = useCallback(
        () => {
            if (topHandler) {
                topHandler();
                return;
            }
            runDebug();
        },
        [runDebug, topHandler]
    );
    return (
        <Button
            type="primary"
            variant="solid"
            loading={debugLoading}
            onClick={handleDebug}
        >运行
        </Button>
    );
}
