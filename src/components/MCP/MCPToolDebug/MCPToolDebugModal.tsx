import {Collapse, Flex, Modal, Typography} from 'antd';
import styled from '@emotion/styled';
import {MCPGlobalVarsForm} from './MCPGlobalVarsForm';
import {MCPToolParamsForm} from './MCPToolParamsForm';
import {MCPToolDebugButton} from './MCPToolDebugButton';
import {MCPToolDebugResult} from './MCPToolDebugResult';

interface Props{
  open: boolean;
  onCancel: () => void;
}
const items = [
    {
        key: '1',
        label: '服务配置',
        children: <MCPGlobalVarsForm />,
    },
];

const modalStyles = {body: {height: '80vh'}};

const Title = styled.div`
  background: linear-gradient(to right, #F5F7FA, #F5F7FA4C);
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  margin: 16px 0px;
`;

const StyledCollapse = styled(Collapse)`
  .ant-5-collapse-header{
    background: linear-gradient(to right, #F5F7FA, #F5F7FA4C);
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    margin: 16px 0px;
  }
`;

const GrowContainer = styled.div`
    flex-grow: 1;
    height: 1px;
    margin-top: 16px;
    .mcp-tool-debug-result-container{
        .json-viewer-container{
            height: 1px;
            flex-grow: 1;
        }
    }
`;
export function MCPToolDebugModal({open, onCancel}: Props) {
    return (
        <Modal
            title="调试"
            open={open}
            onCancel={onCancel}
            footer={null}
            styles={modalStyles}
            width="80vw"
            centered
        >
            <Flex style={{height: '100%'}}>
                <Flex
                    flex="1"
                    style={{height: '100%', borderRight: '1px solid #E8E8E8'}}
                    vertical
                >
                    <Flex flex="0">
                        <Typography.Text
                            type="secondary"
                            style={{fontSize: '14px'}}
                        >请先进行服务配置，再输入工具参数开始调试
                        </Typography.Text>
                    </Flex>
                    <Flex flex="1" vertical style={{overflow: 'auto', paddingRight: '20px'}}>
                        <Flex flex="1" vertical>
                            <Title>工具参数</Title>
                            <Flex vertical>
                                <MCPToolParamsForm />
                            </Flex>
                        </Flex>
                        <Flex flex="0" vertical>
                            <StyledCollapse
                                defaultActiveKey={['1']}
                                ghost
                                items={items}
                                expandIconPosition="end"
                                collapsible="icon"
                            />
                        </Flex>
                    </Flex>
                    <Flex flex="0" justify="center" style={{paddingRight: '20px'}}>
                        <MCPToolDebugButton />
                    </Flex>
                </Flex>
                <Flex flex="2" style={{height: '100%', paddingLeft: '20px'}} vertical>
                    <Typography.Text
                        style={{fontSize: '14px', lineHeight: '22px', fontWeight: '500'}}
                    >运行结果
                    </Typography.Text>
                    <GrowContainer><MCPToolDebugResult card={false} /></GrowContainer>
                </Flex>
            </Flex>
        </Modal>
    );
}
