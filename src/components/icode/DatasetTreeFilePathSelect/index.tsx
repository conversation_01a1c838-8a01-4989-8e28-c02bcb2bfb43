import {useCallback, useMemo, ReactNode, ChangeEvent, cloneElement, CSSProperties} from 'react';
import {TreeSelect, Input} from 'antd';
import {Button} from '@panda-design/components';
import {useDerivedState, useToggle} from 'huse';
import styled from '@emotion/styled';
import stableStringify from 'json-stable-stringify';
import {useFileNode, loadFileNode, getFileNode} from '@/regions/icode/fileTree';
import {useCurrentBranchType} from '@/hooks/icode/current/useCurrentBranchType';
import {FileNode} from '@/types/icode/repo';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';

const StyledDiv = styled.div`
    font-size: 12px;
    width: 120px;
`;

interface Params {
    repo: string;
    commit: string;
}

interface TreeDataNode {
    title?: string | ReactNode;
    value?: string | number;
    children?: TreeDataNode[];
}

const DropdownFooter = styled.div`
    display: flex;
    align-items: center;
    height: 30px;
    margin-top: 5px;
    padding-top: 5px;
    border-top: 1px solid var(--color-gray-5);
    column-gap: 4px;
`;

const toTreeData = (data: FileNode[], {repo, commit}: Params): TreeDataNode[] => {
    const treeData: TreeDataNode[] = [];
    data.forEach(node => {
        const {path, type} = node;
        const children = type === 'TREE' ? getFileNode({repo, commit, path})?.children : undefined;
        if (type === 'TREE') {
            treeData.push({
                title: path,
                value: path,
                children: children ? toTreeData(children, {repo, commit}) : undefined,
            });
        }

    });
    return treeData;
};

interface Props {
    value?: string;
    onChange?: (value: string) => void;
    onSearch?: (value: string) => void;
    style?: CSSProperties;
    popupMatchSelectWidth?: boolean | number;
}

const DatasetTreeFilePathSelect = ({
    value,
    onChange,
    onSearch,
    style = {width: '60%'},
    popupMatchSelectWidth = true,
}: Props) => {
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const fileNode = useFileNode({repo: repoName, commit: refName, path: ''});
    const branchType = useCurrentBranchType();
    const [open, toggleOpen] = useToggle(false);
    const [inputValue, setInputValue] = useDerivedState(value);
    const isDataset = useIsDataset();

    const treeData = useMemo(
        () => {
            if (fileNode?.children) {
                return toTreeData(fileNode?.children, {repo: repoName, commit: refName});
            }
        },
        [fileNode, refName, repoName]
    );

    const handleLoadData = useCallback(
        async (treeNode: TreeDataNode) => {
            const path = treeNode.value as string;
            return loadFileNode({repo: repoName, commit: refName, type: branchType, path, isDataset});
        },
        [repoName, refName, branchType, isDataset]
    );

    const handleOnInputChange = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            setInputValue(e.target.value);
        },
        [setInputValue]
    );

    const handleOnInputSubmit = useCallback(
        () => {
            onChange && onChange(inputValue);
            toggleOpen(false);
        },
        [inputValue, onChange, toggleOpen]
    );

    return (
        <TreeSelect
            style={{...style}}
            key={stableStringify({repo: repoName, commit: refName})}
            showSearch
            value={value}
            // @ts-expect-error
            loadData={handleLoadData}
            treeData={treeData}
            onChange={onChange}
            onSearch={onSearch}
            open={open}
            popupMatchSelectWidth={popupMatchSelectWidth}
            onDropdownVisibleChange={toggleOpen}
            dropdownRender={menu => (
                <div>
                    {cloneElement(menu)}
                    <DropdownFooter>
                        <StyledDiv>自定义目录：</StyledDiv>
                        <Input size="small" onChange={handleOnInputChange} value={inputValue} />
                        <Button size="small" onClick={handleOnInputSubmit}>确定</Button>
                    </DropdownFooter>
                </div>
            )}
        />
    );
};

export default DatasetTreeFilePathSelect;
