/* eslint-disable max-len, max-lines */
import {ColumnProps} from 'antd/es/table';
import {head, isNil, isNumber} from 'lodash';
import {Typography} from 'antd';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {apiTaskRepeatConfirm, TaskGroupInfoItem} from '@/api/ievalue/task';
import {
    MultiType,
    CaseItem,
    MultiModalItem,
    OutputItem,
    ScoreItem,
    StagetegyTaskItem,
    MetricItem,
} from '@/api/ievalue/case';
import {
    TableMDPopover,
    TablePopover,
} from '@/components/ievalue/TableMDPopover';
import {setTaskConclusionModalRegion} from '@/regions/ievalue/task/TaskConclusionModalRegion';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {Evaluate_Split_Str} from '@/constants/ievalue/evaluate';
import {ModelName} from '@/components/Evaluate/ModelName';
import {TableStylePopover} from '@/components/ievalue/TableStyle';
import {SearchResultsProvider} from '@/components/ievalue/SearchResults/SearchResultsProvider';
import {SearchResultsTitle} from '@/components/ievalue/SearchResults/Title';
import {SearchResultsContent} from '@/components/ievalue/SearchResults/SearchResultsContent';

export const getUrlByMultiList = (multiList: MultiType[]) => {
    if (!multiList || multiList.length === 0) {
        return '';
    }
    return multiList.map(e => `!(${e.url})`).join('');
};

export const getMultiModalContent = (multiModal: MultiModalItem) => {
    return (
        getUrlByMultiList(multiModal.audios)
        + getUrlByMultiList(multiModal.videos)
        + getUrlByMultiList(multiModal.images)
        + getUrlByMultiList(multiModal.files)
    );
};

export const hasMultiModalData = (multiModal: MultiModalItem) => {
    return (
        multiModal?.images?.length > 0
        || multiModal?.audios?.length > 0
        || multiModal?.videos?.length > 0
        || multiModal?.files?.length > 0
    );
};

export const getGroupStageNumItemByStageNumber = (
    groupInfo: TaskGroupInfoItem,
    stageNumber: number
) => {
    return groupInfo?.caseStageNum?.find(
        item => stageNumber === item.stageID
    );
};

export const getModelScoreColumn = (
    dataSource: CaseItem[],
    stageID: number,
    datasetID: number,
    strategyInfo: StagetegyTaskItem,
    refresh: () => void,
    includesPredicting?: boolean,
    isAutoEvaluate?: boolean
) => {
    return (head(dataSource)?.output ?? []).reduce(
        (resultList: any[], item: OutputItem, index: number) => {
            const modelTitle = (
                <ModelName
                    realName={item.modelName}
                    pretendName={`结果${index + 1}`}
                    stageID={stageID}
                />
            );
            const outputColumn: ColumnProps<CaseItem> = {
                title: '输出',
                dataIndex: ['output', index],
                key: `result_${index}_output`,
                ellipsis: {
                    showTitle: false,
                },
                render: (outputItem: OutputItem, record: CaseItem) => {
                    return (
                        <TableMDPopover
                            content={outputItem?.modelOutput}
                            datasetID={datasetID}
                            onRefresh={refresh}
                            titleName={modelTitle}
                            caseID={record.caseID}
                            groupID={record.groupID}
                            stageID={record.stageID}
                            recordID={outputItem.recordID}
                            outputMultiData={outputItem?.outputMultiData}
                        />
                    );
                },
            };
            const timeUsedColumn: ColumnProps<CaseItem> = {
                title: '推理耗时（秒）',
                dataIndex: ['output', index],
                key: `result_${index}_timeUsed`,
                render: (outputItem: OutputItem) => {
                    return (
                        <span>
                            {outputItem?.status === 'SUCCESS'
                                ? outputItem?.timeUsed
                                : '--'}
                        </span>
                    );
                },
            };
            const scoreColumnList: any[] = strategyInfo?.metric?.reduce(
                (
                    results: any[],
                    metricItem: MetricItem,
                    metricIndex: number
                ) => {
                    const filters = dataSource
                        ?.reduce((filterList: any[], record) => {
                            const scoreItem =
                                record.output?.[index]?.score?.[metricIndex];
                            if (scoreItem) {
                                const displayValue = metricItem?.noScoring
                                    ? scoreItem?.scoreName
                                    : scoreItem?.scoreInput ?? scoreItem?.score;
                                if (
                                    isNumber(displayValue)
                                    && !filterList.some(
                                        item => item.value === displayValue
                                    )
                                ) {
                                    filterList.push({
                                        text: `${displayValue}`,
                                        value: displayValue,
                                    });
                                }
                            }
                            return filterList;
                        }, [])
                        ?.sort((a, b) => a.value - b.value);
                    results.push({
                        title: metricItem.desc,
                        width: 90,
                        dataIndex: ['output', index, 'score', metricIndex],
                        key: `result_${index}_score_${metricIndex}`,
                        ellipsis: {
                            showTitle: false,
                        },
                        filterSearch: true,
                        filters,
                        onFilter: (value: string, record: any) => {
                            const scoreItem =
                                record.output?.[index]?.score?.[metricIndex];
                            const result = metricItem?.noScoring
                                ? scoreItem?.scoreName
                                : scoreItem?.scoreInput ?? scoreItem?.score;
                            return result === value;
                        },
                        render: (scoreItem: ScoreItem) => {
                            const result = metricItem?.noScoring
                                ? scoreItem?.scoreName
                                : scoreItem?.scoreInput
                                  ?? scoreItem?.score
                                  ?? '--';
                            return (
                                <Typography.Text
                                    ellipsis={{tooltip: true}}
                                    style={{maxWidth: 150}}
                                >
                                    {result}
                                </Typography.Text>
                            );
                        },
                    });
                    if (
                        isAutoEvaluate
                        && !isNil(item?.score?.[metricIndex]?.output)
                    ) {
                        results.push({
                            title: `${metricItem?.desc}打分原因`,
                            dataIndex: [
                                'output',
                                index,
                                'score',
                                metricIndex,
                                'output',
                            ],
                            key: `${item?.modelName}${Evaluate_Split_Str}${metricItem?.metric}${Evaluate_Split_Str}output`,
                            width: 230,
                            render: (output: any, record: any) => {
                                return (
                                    <TableMDPopover
                                        content={output}
                                        datasetID={datasetID}
                                        caseID={record.caseID}
                                        groupID={record.groupID}
                                        stageID={record.stageID}
                                    />
                                );
                            },
                        });
                    }
                    return results;
                },
                []
            );
            const noteColumn: ColumnProps<CaseItem> = {
                title: '备注',
                width: 150,
                dataIndex: ['output', index, 'note'],
                key: `result_${index}_note`,
                ellipsis: {
                    showTitle: false,
                },
                render: (note: string) => {
                    return <TablePopover content={note} />;
                },
            };
            const {reasoningContent, searchResults} = item || {};
            const reasoningContentColumn = {
                title: '思考过程',
                dataIndex: ['output', index],
                key: `reasoningContent${index}`,
                render: (outputItem: OutputItem) => {
                    return (
                        <TableStylePopover
                            content={outputItem?.reasoningContent}
                        />
                    );
                },
            };
            const searchResultsColumn = {
                title: '联网搜索',
                dataIndex: ['output', index],
                key: `searchResults${index}`,
                render: (outputItem: OutputItem) => {
                    return (
                        <SearchResultsProvider
                            data={outputItem?.searchResults}
                            ellipsis
                            noStyle
                        >
                            <TableStylePopover
                                title={<SearchResultsTitle />}
                                content={<SearchResultsContent />}
                            />
                        </SearchResultsProvider>
                    );
                },
            };
            if (strategyInfo) {
                const children = [];
                if (reasoningContent) {
                    children.push(reasoningContentColumn);
                }
                children.push(outputColumn);
                if (searchResults?.length) {
                    children.push(searchResultsColumn);
                }
                if (includesPredicting) {
                    children.push(timeUsedColumn);
                }
                children.push(...[...scoreColumnList, noteColumn]);
                resultList.push({
                    title: (
                        <FlexLayout justify="center">{modelTitle}</FlexLayout>
                    ),
                    children,
                });
            } else if (includesPredicting) {
                const culs = {
                    ...outputColumn,
                    timeUsedColumn,
                    reasoningContentColumn,
                    searchResultsColumn,
                    title: (
                        <FlexLayout justify="center">{modelTitle}</FlexLayout>
                    ),
                };
                resultList.push(culs);
            } else {
                resultList.push({
                    ...outputColumn,
                    title: (
                        <FlexLayout justify="center">{modelTitle}</FlexLayout>
                    ),
                });
            }
            return resultList;
        },
        []
    );
};

export const handleTaskRepeatConfirm = (
    isMegAgent: boolean,
    taskID: number,
    onFinish: () => void,
    isTerminated?: boolean
) => {
    return async () => {
        if (isMegAgent) {
            const response = await apiTaskRepeatConfirm(
                isTerminated
                    ? {taskID, stage: TaskStageEnum.TERMINATED}
                    : {taskID}
            );
            if (response?.repeatConfirm) {
                setTaskConclusionModalRegion({
                    open: true,
                    initValue: {taskID, ...response},
                    onFinish,
                });
                return;
            }
        }
        onFinish();
    };
};
