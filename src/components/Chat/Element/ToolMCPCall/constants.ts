export const UI_DIMENSIONS = {
    TITLE_FONT_SIZE: 14,
    SUBTITLE_FONT_SIZE: 12,
    BUTTON_ICON_SIZE: 10,
    ICON_SIZE: 14,
    FONT_SIZE_LARGE: 16,

    TITLE_LINE_HEIGHT: '22px',
    SUBTITLE_LINE_HEIGHT: '20px',

    MAIN_GAP: 8,
    ICON_GAP: 4,
    SECTION_MARGIN_TOP: '10px',

    BUTTON_SIZE: 16,
    BUTTON_PADDING: 0,
    FULL_WIDTH: '100%',

    LETTER_SPACING: 0,
} as const;

export const UI_COLORS = {
    SEPARATOR_COLOR: '#d9d9d9',
    SUBTITLE_COLOR: '#666',
    toolMcpCallBg: 'linear-gradient(180deg, #F5F7FA 0%, rgba(245, 247, 250, 0.4) 100%)',
    toolMcpCallContentBg: '#FFFFFF',
} as const;

export const COMPONENT_CONFIGS = {
    TITLE_FONT_WEIGHT: 500,
    SUBTITLE_FONT_WEIGHT: 400,
} as const;

export const STYLE_VALUES = {
    CONTAINER_WIDTH: '100%',

    CARD_BORDER: 'none',
    CARD_BOX_SHADOW: 'none',

    ARGUMENTS_TITLE_PADDING: '3px 0',

    CONTENT_MARGIN: '0',
    CONTENT_PADDING: '12px 16px',
    CONTENT_MAX_HEIGHT: '200px',
    CONTENT_BORDER_RADIUS: '6px',
    CONTENT_BORDER: '1px solid #D9D9D9',
} as const;

export const ANIMATION_VALUES = {
    COPY_BUTTON_OPACITY_DEFAULT: 0.6,
    COPY_BUTTON_OPACITY_HOVER: 1,
    COPY_NOTIFICATION_DURATION: 2500,

    TRANSITION_DURATION: '0.2s',
    OPACITY_TRANSITION: 'opacity 0.2s',
} as const;

