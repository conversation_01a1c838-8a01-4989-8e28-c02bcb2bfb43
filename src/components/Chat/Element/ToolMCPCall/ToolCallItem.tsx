import {Button} from '@panda-design/components';
import {Flex} from 'antd';
import {DownOutlined, RightOutlined} from '@ant-design/icons';
import {IconParams, IconResult} from '@/icons/mcp';
import {MCPToolCall} from '@/types/mcp/mcp';
import {ToolCallCard, ArgumentsTitle, ArgumentsContent} from './styles';
import {formatArguments, formatResult, getToolCallProps} from './utils';
import CopyButton from './CopyButton';
import {UI_DIMENSIONS, UI_COLORS, COMPONENT_CONFIGS} from './constants';

interface ToolCallItemProps {
    toolCall: MCPToolCall;
    toolId: string;
    isExpanded: boolean;
    onToggleExpanded: (toolId: string) => void;
}

const ToolCallItem = ({
    toolCall,
    toolId,
    isExpanded,
    onToggleExpanded,
}: ToolCallItemProps) => {
    const {hasArguments, hasResult, canToggle} = getToolCallProps(toolCall);

    return (
        <ToolCallCard key={toolId} className="success" size="small">
            <Flex
                align="center"
                justify="space-between"
                style={{width: UI_DIMENSIONS.FULL_WIDTH}}
            >
                <Flex align="center" gap={UI_DIMENSIONS.MAIN_GAP}>
                    <span
                        style={{
                            fontWeight: COMPONENT_CONFIGS.TITLE_FONT_WEIGHT,
                            fontSize: UI_DIMENSIONS.TITLE_FONT_SIZE,
                            lineHeight: UI_DIMENSIONS.TITLE_LINE_HEIGHT,
                            letterSpacing: UI_DIMENSIONS.LETTER_SPACING,
                        }}
                    >
                        调用MCP工具
                    </span>
                    <span
                        style={{
                            color: UI_COLORS.SEPARATOR_COLOR,
                            fontSize: UI_DIMENSIONS.SUBTITLE_FONT_SIZE,
                        }}
                    >
                        |
                    </span>
                    <span
                        style={{
                            fontWeight: COMPONENT_CONFIGS.SUBTITLE_FONT_WEIGHT,
                            fontSize: UI_DIMENSIONS.SUBTITLE_FONT_SIZE,
                            lineHeight: UI_DIMENSIONS.SUBTITLE_LINE_HEIGHT,
                            letterSpacing: UI_DIMENSIONS.LETTER_SPACING,
                            color: UI_COLORS.SUBTITLE_COLOR,
                        }}
                    >
                        {`${toolCall.tool.serverName}/${toolCall.tool.toolName}`}
                    </span>
                </Flex>
                {canToggle && (
                    <Button
                        icon={isExpanded ? <DownOutlined /> : <RightOutlined />}
                        onClick={() => onToggleExpanded(toolId)}
                        style={{
                            fontSize: UI_DIMENSIONS.BUTTON_ICON_SIZE,
                            padding: UI_DIMENSIONS.BUTTON_PADDING,
                            width: UI_DIMENSIONS.BUTTON_SIZE,
                            height: UI_DIMENSIONS.BUTTON_SIZE,
                        }}
                    />
                )}
            </Flex>

            {isExpanded && (
                <>
                    {hasArguments && (
                        <div
                            style={{
                                marginTop: UI_DIMENSIONS.SECTION_MARGIN_TOP,
                            }}
                        >
                            <Flex justify="space-between" align="center">
                                <Flex
                                    align="center"
                                    gap={UI_DIMENSIONS.ICON_GAP}
                                >
                                    <IconParams
                                        style={{
                                            fontSize: UI_DIMENSIONS.ICON_SIZE,
                                        }}
                                    />
                                    <ArgumentsTitle>参数</ArgumentsTitle>
                                </Flex>
                                <CopyButton
                                    text={formatArguments(toolCall.input)}
                                />
                            </Flex>
                            <ArgumentsContent>
                                {formatArguments(toolCall.input)}
                            </ArgumentsContent>
                        </div>
                    )}

                    {hasResult && (
                        <div
                            style={{
                                marginTop: UI_DIMENSIONS.SECTION_MARGIN_TOP,
                            }}
                        >
                            <Flex justify="space-between" align="center">
                                <Flex
                                    align="center"
                                    gap={UI_DIMENSIONS.ICON_GAP}
                                >
                                    <IconResult
                                        style={{
                                            fontSize: UI_DIMENSIONS.ICON_SIZE,
                                        }}
                                    />
                                    <ArgumentsTitle>结果</ArgumentsTitle>
                                </Flex>
                                <CopyButton
                                    text={formatResult(toolCall.output)}
                                />
                            </Flex>
                            <ArgumentsContent>
                                {formatResult(toolCall.output)}
                            </ArgumentsContent>
                        </div>
                    )}
                </>
            )}
        </ToolCallCard>
    );
};

export default ToolCallItem;
