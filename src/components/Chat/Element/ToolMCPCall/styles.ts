import {Card, Typography} from 'antd';
import styled from '@emotion/styled';
import {
    UI_DIMENSIONS,
    STYLE_VALUES,
    ANIMATION_VALUES,
    COMPONENT_CONFIGS,
    UI_COLORS,
} from './constants';

const {Text} = Typography;

export const Container = styled.div`
    width: ${STYLE_VALUES.CONTAINER_WIDTH};
`;

export const ToolCallCard = styled(Card)`
    border: ${STYLE_VALUES.CARD_BORDER};
    box-shadow: ${STYLE_VALUES.CARD_BOX_SHADOW};
    background: ${UI_COLORS.toolMcpCallBg};
`;

export const CopyButtonWrapper = styled.div`
    opacity: ${ANIMATION_VALUES.COPY_BUTTON_OPACITY_DEFAULT};
    transition: ${ANIMATION_VALUES.OPACITY_TRANSITION};
    &:hover {
        opacity: ${ANIMATION_VALUES.COPY_BUTTON_OPACITY_HOVER};
    }
`;

export const ArgumentsTitle = styled(Text)`
    font-size: ${UI_DIMENSIONS.TITLE_FONT_SIZE}px;
    font-weight: ${COMPONENT_CONFIGS.TITLE_FONT_WEIGHT};
    line-height: ${UI_DIMENSIONS.TITLE_LINE_HEIGHT};
    padding: ${STYLE_VALUES.ARGUMENTS_TITLE_PADDING};
`;

export const ArgumentsContent = styled.pre`
    margin: ${STYLE_VALUES.CONTENT_MARGIN};
    padding: ${STYLE_VALUES.CONTENT_PADDING};
    background: ${UI_COLORS.toolMcpCallContentBg};
    font-size: ${UI_DIMENSIONS.SUBTITLE_FONT_SIZE}px;
    white-space: 'pre-wrap';
    word-break: 'break-all';
    max-height: ${STYLE_VALUES.CONTENT_MAX_HEIGHT};
    overflow-y: 'auto';
    border-radius: ${STYLE_VALUES.CONTENT_BORDER_RADIUS};
    border: ${STYLE_VALUES.CONTENT_BORDER};
`;
