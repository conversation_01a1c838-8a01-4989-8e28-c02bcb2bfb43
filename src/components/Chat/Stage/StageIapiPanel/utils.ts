import {DirectoryResponseData, Team} from '@/api/icode/apidoc';

type IHttpFolderOption = DirectoryResponseData & {
  value: number;
  children?: IHttpFolderOption[];
};

// 遍历树结构
function traverseTree(tree: IHttpFolderOption[], callback: (node: IHttpFolderOption) => void) {
    for (const node of tree) {
        callback(node);
        if (node.children && node.children.length > 0) {
            traverseTree(node.children, callback);
        }
    }
}

export function transformTreeData(data: DirectoryResponseData[], root: DirectoryResponseData) {
    const treeData: IHttpFolderOption[] = [];
    for (const folder of data) {
    // 根节点
        if (folder.parentId === root.id || folder.parentId === 0) {
            treeData.push({...folder, value: folder.id, children: []});
        } else {
            traverseTree(treeData, node => {
                if (node.id === folder.parentId) {
                    if (!node.children) {
                        node.children = [];
                    }
                    node.children.push({...folder, value: folder.id, children: []});
                }
            });
        }
    }
    return treeData;
}

// 是否存在teamId
export function hasTeamIdAndProjectId(data: Team[], teamId: number, projectId: number) {
    const index = data.findIndex(item => item.id === teamId);
    if (index !== -1) {
        return data[index].children.findIndex(item => item.id === projectId) !== -1;
    }
    return false;
}
