/* eslint-disable max-len */
/* eslint-disable max-lines */
import {Cascader, Col, Divider, Flex, Form, Input, Row, Select, Space, Tooltip, TreeSelect} from 'antd';
import {But<PERSON>, Modal} from '@panda-design/components';
import {cloneElement, useCallback, useMemo, useState, ReactElement} from 'react';
import styled from '@emotion/styled';
import {LeftOutlined, PlusOutlined, QuestionCircleOutlined, UpOutlined} from '@ant-design/icons';
import {TreeNode} from 'antd/es/tree-select';
import {css} from '@emotion/css';
import {useToggle} from 'huse';
import {AntTreeNodeProps} from 'antd/es/tree';
import {
    DirectoryResponseData,
    Team, createNewDirectory,
    getProjectDirectories,
    updateDirectory,
} from '@/api/icode/apidoc';
import {getSysToken} from '@/utils/icode/api/createBApiInterface';
import {stopPropagation} from '@/utils/eventHandler';
import {useSupportAreaFull} from '@/regions/staff/supportArea';
import {transformTreeData} from './utils';

interface IdentityPattern {
    type: string;
    bodyType: string;
    fields: string[];
}

interface SaveOptionProps {
    username: string;
    projectOption: Team[];
    teamIdAndProjectId: number[];
    setTeamIdAndProjectId: (value: number[]) => void;
    initialFolderOption: DirectoryResponseData[];
    setInitialFolderOption: (value: DirectoryResponseData[]) => void;
    folderOption: DirectoryResponseData[];
    setFolderOption: (value: DirectoryResponseData[]) => void;
    folderId: number | undefined;
    setFolderId: (value: number | undefined) => void;
    interfaceSign: IdentityPattern;
    setInterfaceSign: (value: IdentityPattern) => void;
    apiOverwriteMode: string;
    setApiOverwriteMode: (value: string) => void;
}

const ContentRow = styled(Row)`
    margin-bottom: 16px !important;
`;

const TreeSelectTitle = styled.div`
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;

    .tree-node-title {
        width: 95% !important;
        padding: 4px 0px !important;
    }

    .icon {
        display: none !important;
    }

    &:hover .icon {
        margin-right: 8px !important;
        display: inline-block !important;
    }
`;

const CustomizedTreeSelect = styled(TreeSelect)`
    width: 250px !important;
    
    .ant-5-select-selection-item {
        pointer-events: none !important;   

        &:hover .icon{
            display: none !important;
        }
    }
`;

const CustomizedForm = styled.div`
    padding: 4px 16px !important;
`;

const GoBackButton = styled(Button)`
    justify-content: flex-start !important;
    width: 95% !important;
    margin-left: -10px !important;
    margin-bottom: 16px !important;
`;

const AddSubFolderButton = styled(Button)`
    justify-content: flex-start !important;
    align-items: center !important;
    width: 95% !important;
    margin-left: 4px !important;
`;

const EditUniqueValueButton = styled(Button)`
    color: #5b82ee !important;
    &:hover {
        background-color: transparent !important;
    }
`;

const EllipsisText = styled.div`
    max-width: 135px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
`;

export const SaveOption = ({
    username,
    projectOption,
    teamIdAndProjectId,
    setTeamIdAndProjectId,
    initialFolderOption,
    setInitialFolderOption,
    folderOption,
    setFolderOption,
    folderId,
    setFolderId,
    interfaceSign,
    setInterfaceSign,
    apiOverwriteMode,
    setApiOverwriteMode,
}: SaveOptionProps) => {
    const supportAreaFull = useSupportAreaFull();
    const [dropDownOpen, dropDownToggleOpen] = useToggle(false);
    const [isRenderForm, setIsRenderForm] = useState(false);
    const [folderParentId, setFolderParentId] = useState<number>();
    const [createLoading, setCreateLoading] = useState(false);
    const [modalOpen, modalToggleOpen] = useToggle(false);
    const [updateLoading, setUpdateLoading] = useState(false);

    const cascaderChange = useCallback(
        async (value: number[]) => {
            setTeamIdAndProjectId(value);
            if (value) {
                setApiOverwriteMode('methodAndPath');
                const result = await getProjectDirectories({
                    headers: {
                        'x-baidu-username': username,
                        'x-project-id': String(value[1]),
                    },
                    params: {
                        sysToken: getSysToken(),
                    },
                });
                setInitialFolderOption(result);
                setFolderOption(transformTreeData(result, result[0]));
                setFolderId(result[0].id);
                setInterfaceSign(result[0].identityPattern.httpApi);
            } else {
                setFolderId(undefined);
                setInterfaceSign(null);
                setApiOverwriteMode('');
            }
            setIsRenderForm(false);
        },
        [
            setApiOverwriteMode,
            setFolderId,
            setFolderOption,
            setInitialFolderOption,
            setInterfaceSign,
            setTeamIdAndProjectId,
            username,
        ]
    );

    const treeSelectChange = useCallback(
        (id: number) => {
            setFolderId(id);
            dropDownToggleOpen(false);
        },
        [dropDownToggleOpen, setFolderId]
    );

    const goBack = useCallback(
        () => {
            setIsRenderForm(false);
        },
        []
    );

    const getFolderParentName = useCallback(
        (parentId: number, data: DirectoryResponseData[], root: DirectoryResponseData): string => {
            const parent = data.find(item => item.id === parentId);
            if (parent.parentId === root.id || parent.parentId === 0) {
                return parent.name;
            } else {
                return getFolderParentName(parent.parentId, data, root) + '/' + parent.name;
            }
        },
        []
    );

    const createNewFolder = useCallback(
        async (values: {folderName: string}) => {
            setCreateLoading(true);
            const folder = await createNewDirectory({
                headers: {
                    'x-baidu-username': username,
                    'x-project-id': String(teamIdAndProjectId[1]),
                    'Content-type': 'application/x-www-form-urlencoded',
                },
                payload: {
                    name: values.folderName,
                    parentId: String(folderParentId),
                },
                params: {
                    sysToken: getSysToken(),
                },
            });
            const result = await getProjectDirectories({
                headers: {
                    'x-baidu-username': username,
                    'x-project-id': String(teamIdAndProjectId[1]),
                },
                params: {
                    sysToken: getSysToken(),
                },
            });
            setInitialFolderOption(result);
            setFolderOption(transformTreeData(result, result[0]));
            setCreateLoading(false);
            setFolderId(folder.id);
            setIsRenderForm(false);
            dropDownToggleOpen(false);
        },
        [
            dropDownToggleOpen,
            folderParentId,
            setFolderId,
            setFolderOption,
            setInitialFolderOption,
            teamIdAndProjectId,
            username,
        ]
    );

    const switchTreeSelectRender = useCallback(
        (id: number) => {
            setIsRenderForm(true);
            setFolderId(undefined);
            setFolderParentId(id);
        },
        [setFolderId, setIsRenderForm]
    );

    const updateInterfaceSign = useCallback(
        async (values: {strategy: string, customizeParameter?: string[], bodyType: string}) => {
            setUpdateLoading(true);
            await updateDirectory({
                headers: {
                    'x-baidu-username': username,
                    'x-project-id': String(teamIdAndProjectId[1]),
                    'Content-type': 'application/x-www-form-urlencoded',
                },
                payload: {
                    id: String(initialFolderOption[0].id),
                    parentId: '0',
                    identityPattern: {
                        httpApi: {
                            type: values.strategy,
                            bodyType: values?.bodyType,
                            fields: values?.customizeParameter,
                        },
                    },
                },
                params: {
                    sysToken: getSysToken(),
                },
                folderId: String(initialFolderOption[0].id),
            });
            const result = await getProjectDirectories({
                headers: {
                    'x-baidu-username': username,
                    'x-project-id': String(teamIdAndProjectId[1]),
                },
                params: {
                    sysToken: getSysToken(),
                },
            });
            setInitialFolderOption(result);
            setFolderOption(transformTreeData(result, result[0]));
            setInterfaceSign(result[0].identityPattern.httpApi);
            setUpdateLoading(false);
            modalToggleOpen(false);
        },
        [
            initialFolderOption,
            modalToggleOpen,
            setFolderOption,
            setInitialFolderOption,
            setInterfaceSign,
            teamIdAndProjectId, username,
        ]
    );

    const renderTreeNodes = useCallback(
        (data: DirectoryResponseData[]) => {
            return data.map(item => {
                if (item.children && item.children.length > 0) {
                    return (
                        <TreeNode
                            key={item.id}
                            value={item.id}
                            name={item.name}
                            title={
                                <TreeSelectTitle onClick={stopPropagation}>
                                    <span
                                        className="tree-node-title"
                                        onClick={() => treeSelectChange(item.id)}
                                    >
                                        {item.name}
                                    </span>
                                    <Tooltip title="添加子目录">
                                        <PlusOutlined
                                            className="icon"
                                            onClick={() => switchTreeSelectRender(item.id)}
                                        />
                                    </Tooltip>
                                </TreeSelectTitle>
                            }
                        >
                            {renderTreeNodes(item.children)}
                        </TreeNode>
                    );
                }
                return (
                    <TreeNode
                        key={item.id}
                        value={item.id}
                        name={item.name}
                        isLeaf
                        title={
                            <TreeSelectTitle onClick={stopPropagation}>
                                <span
                                    className="tree-node-title"
                                    onClick={() => treeSelectChange(item.id)}
                                >
                                    {item.name}
                                </span>
                                <Tooltip title="添加子目录">
                                    <PlusOutlined className="icon" onClick={() => switchTreeSelectRender(item.id)} />
                                </Tooltip>
                            </TreeSelectTitle>
                        }
                    />
                );
            });
        },
        [switchTreeSelectRender, treeSelectChange]
    );

    const interfaceSignName = useMemo(
        () => {
            let fields = '';
            switch (interfaceSign?.type) {
                case 'methodAndPath':
                    return 'Method & Path';
                case 'operationId':
                    return 'OperationId';
                case 'query':
                    for (const field of interfaceSign?.fields) {
                        fields += field + '&';
                    }
                    return `Query 参数(${fields.substring(0, fields.length - 1)})`;
                case 'body':
                    for (const field of interfaceSign?.fields) {
                        fields += field + '&';
                    }
                    return `Body 参数(${fields.substring(0, fields.length - 1)})`;
                case 'header':
                    for (const field of interfaceSign?.fields) {
                        fields += field + '&';
                    }
                    return `Header 参数(${fields.substring(0, fields.length - 1)})`;
                default:
                    return '选择项目后展示';
            }
        },
        [interfaceSign]
    );

    const renderTreeDropDown = (menu: ReactElement) => {
        return (
            <>
                {
                    isRenderForm ? (
                        <CustomizedForm>
                            {
                                folderParentId === 0 ? (
                                    <>
                                        <GoBackButton
                                            icon={<LeftOutlined />}
                                            type="text"
                                            onClick={goBack}
                                        >
                                            添加目录
                                        </GoBackButton>
                                        <Form
                                            name="add-sub-folder"
                                            layout="vertical"
                                            onFinish={createNewFolder}
                                        >
                                            <Form.Item
                                                layout="vertical"
                                                label="添加一级目录"
                                                name="folderName"
                                                rules={[{required: true, message: '请输入一级目录名称'}]}
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                style={{marginBottom: 20}}
                                            >
                                                <Input placeholder="请输入一级目录名称" />
                                            </Form.Item>
                                            <Form.Item label={null} style={{marginBottom: 20}}>
                                                <Flex justify="flex-end">
                                                    <Button onClick={goBack} style={{marginRight: 12}}>取消</Button>
                                                    <Button type="primary" htmlType="submit" loading={createLoading}>
                                                        确定
                                                    </Button>
                                                </Flex>
                                            </Form.Item>
                                        </Form>
                                    </>
                                ) : (
                                    <>
                                        <GoBackButton
                                            icon={<LeftOutlined />}
                                            type="text"
                                            onClick={goBack}
                                        >
                                            添加子目录
                                        </GoBackButton>
                                        <Form
                                            name="add-sub-folder"
                                            layout="vertical"
                                            onFinish={createNewFolder}
                                        >
                                            <Form.Item
                                                layout="vertical"
                                                label="添加子目录"
                                                name="folderName"
                                                rules={[{required: true, message: '请输入子目录名称'}]}
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                style={{marginBottom: 16}}
                                            >
                                                <Input placeholder="请输入子目录名称" />
                                            </Form.Item>
                                            <Form.Item
                                                layout="vertical"
                                                label="父级目录"
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                style={{marginBottom: 20}}
                                            >
                                                <Select
                                                    style={{pointerEvents: 'none'}}
                                                    disabled
                                                    defaultValue={folderParentId}
                                                    options={[{label: `${getFolderParentName(folderParentId, initialFolderOption, initialFolderOption[0])}`, value: folderParentId}]}
                                                />
                                            </Form.Item>
                                            <Form.Item label={null} style={{marginBottom: 20}}>
                                                <Flex justify="flex-end">
                                                    <Button onClick={goBack} style={{marginRight: 12}}>取消</Button>
                                                    <Button type="primary" htmlType="submit" loading={createLoading}>
                                                        确定
                                                    </Button>
                                                </Flex>
                                            </Form.Item>
                                        </Form>
                                    </>
                                )
                            }
                        </CustomizedForm>
                    ) : (
                        <>
                            {cloneElement(menu)}
                            <Divider style={{margin: '8px 0'}} />
                            <AddSubFolderButton
                                onClick={() => switchTreeSelectRender(0)}
                                icon={<PlusOutlined />}
                                type="text"
                            >
                                添加目录
                            </AddSubFolderButton>
                        </>
                    )
                }
            </>
        );
    };

    return (
        <>
            {supportAreaFull ? (
                <ContentRow justify="center" align="middle">
                    <Col flex="120px" style={{color: '#545455'}}>保存至iAPI平台</Col>
                    <Col flex="508px" style={{marginRight: 100}}>
                        <Cascader
                            placeholder="请选择项目"
                            options={projectOption}
                            fieldNames={{label: 'name', value: 'id'}}
                            value={teamIdAndProjectId}
                            style={{width: 250, marginRight: 8}}
                            onChange={cascaderChange}
                        />
                        <CustomizedTreeSelect
                            placeholder={!teamIdAndProjectId ? '请完成项目选择' : '请选择目录'}
                            disabled={!teamIdAndProjectId}
                            rootClassName={css`
                                    .ant-5-select-tree-treenode{
                                        margin-bottom: 0 !important;
                                        margin-left: 6px !important;
                                        padding-bottom: 4px !important;
                                        &: before{
                                            display: none !important;
                                        }

                                        .ant-5-select-tree-switcher {
                                            margin: 4px 0px !important;
                                        }
                                    }
                                `}
                            switcherIcon={
                                (props: AntTreeNodeProps) => <UpOutlined rotate={props.expanded ? 0 : 180} />
                            }
                            getPopupContainer={() => document.querySelector('#iapiContainer')}
                            onDropdownVisibleChange={dropDownToggleOpen}
                            showSearch={!isRenderForm}
                            filterTreeNode={(inputValue, treeNode) => {
                                return treeNode.name.includes(inputValue);
                            }}
                            value={folderId}
                            open={dropDownOpen}
                            treeDefaultExpandedKeys={[folderParentId]}
                            dropdownRender={menu => renderTreeDropDown(menu)}
                        >
                            {renderTreeNodes(folderOption)}
                        </CustomizedTreeSelect>
                    </Col>
                    <Col flex="80px" style={{color: '#545455'}}>更新策略</Col>
                    <Col flex="auto">
                        <Space size="middle">
                            <div>
                                匹配到相同接口时（根据
                                <EditUniqueValueButton
                                    disabled={!teamIdAndProjectId}
                                    type="link"
                                    onClick={modalToggleOpen}
                                >
                                    <EllipsisText>{interfaceSignName}</EllipsisText>
                                </EditUniqueValueButton>
                                ）
                            </div>
                            <Select
                                disabled={!teamIdAndProjectId}
                                placeholder={!teamIdAndProjectId ? '请完成项目选择' : '请选择更新策略'}
                                value={!teamIdAndProjectId ? undefined : apiOverwriteMode}
                                options={[
                                    {label: '覆盖已有接口', value: 'methodAndPath'},
                                    {
                                        label: (
                                            <span>
                                                智能合并
                                                <Tooltip title="保留已经修改的 Mock、中文名、说明、请求示例、响应示例等">
                                                    <QuestionCircleOutlined
                                                        style={{fontSize: 12, marginLeft: 6, color: 'gray'}}
                                                    />
                                                </Tooltip>
                                            </span>
                                        ),
                                        value: 'merge',

                                    },
                                    {label: '不导入', value: 'ignore'},
                                    {label: '保留两者', value: 'both'},
                                ]}
                                onChange={setApiOverwriteMode}
                                style={{width: 200}}
                            />
                        </Space>
                    </Col>
                </ContentRow>
            ) : (
                <>
                    <ContentRow style={{marginTop: -8}} justify="center" align="middle">
                        <Col flex="120px" style={{color: '#545455'}}>保存至iAPI平台</Col>
                        <Col flex="auto">
                            <Cascader
                                placeholder="请选择项目"
                                options={projectOption}
                                fieldNames={{label: 'name', value: 'id'}}
                                value={teamIdAndProjectId}
                                style={{width: 250, marginRight: 8}}
                                onChange={cascaderChange}
                            />
                            <CustomizedTreeSelect
                                placeholder={!teamIdAndProjectId ? '请完成项目选择' : '请选择目录'}
                                disabled={!teamIdAndProjectId}
                                rootClassName={css`
                                        .ant-5-select-tree-treenode{
                                            margin-bottom: 0 !important;
                                            margin-left: 6px !important;
                                            padding-bottom: 4px !important;
                                            &: before{
                                                display: none !important;
                                            }

                                            .ant-5-select-tree-switcher {
                                                margin: 4px 0px !important;
                                            }
                                        }
                                    `}
                                switcherIcon={
                                    (props: AntTreeNodeProps) => <UpOutlined rotate={props.expanded ? 0 : 180} />
                                }
                                getPopupContainer={() => document.querySelector('#iapiContainer')}
                                onDropdownVisibleChange={dropDownToggleOpen}
                                showSearch={!isRenderForm}
                                filterTreeNode={(inputValue, treeNode) => {
                                    return treeNode.name.includes(inputValue);
                                }}
                                value={folderId}
                                open={dropDownOpen}
                                treeDefaultExpandedKeys={[folderParentId]}
                                dropdownRender={menu => renderTreeDropDown(menu)}
                            >
                                {renderTreeNodes(folderOption)}
                            </CustomizedTreeSelect>
                        </Col>
                    </ContentRow>
                    <ContentRow style={{marginBottom: 20}} justify="center" align="middle">
                        <Col flex="120px" style={{color: '#545455'}}>更新策略</Col>
                        <Col flex="auto">
                            <Space size="middle">
                                <div>
                                    匹配到相同接口时（根据
                                    <EditUniqueValueButton
                                        disabled={!teamIdAndProjectId}
                                        type="link"
                                        onClick={modalToggleOpen}
                                    >
                                        <EllipsisText>{interfaceSignName}</EllipsisText>
                                    </EditUniqueValueButton>
                                    ）
                                </div>
                                <Select
                                    disabled={!teamIdAndProjectId}
                                    placeholder={!teamIdAndProjectId ? '请完成项目选择' : '请选择更新策略'}
                                    value={!teamIdAndProjectId ? undefined : apiOverwriteMode}
                                    options={[
                                        {label: '覆盖已有接口', value: 'methodAndPath'},
                                        {
                                            label: (
                                                <span>
                                                    智能合并
                                                    <Tooltip title="保留已经修改的 Mock、中文名、说明、请求示例、响应示例等">
                                                        <QuestionCircleOutlined
                                                            style={{fontSize: 12, marginLeft: 6, color: 'gray'}}
                                                        />
                                                    </Tooltip>
                                                </span>
                                            ),
                                            value: 'merge',

                                        },
                                        {label: '不导入', value: 'ignore'},
                                        {label: '保留两者', value: 'both'},
                                    ]}
                                    onChange={setApiOverwriteMode}
                                    style={{width: 200}}
                                />
                            </Space>
                        </Col>
                    </ContentRow>
                </>
            )}
            <Modal
                title={<span style={{fontSize: 16}}>编辑接口唯一标识</span>}
                closable
                open={modalOpen}
                footer={null}
                destroyOnClose
                onCancel={modalToggleOpen}
            >
                <Form
                    onFinish={updateInterfaceSign}
                    initialValues={{
                        strategy: interfaceSign?.type,
                        customizeParameter: interfaceSign?.fields,
                        bodyType: interfaceSign?.bodyType,
                    }}
                >
                    <Form.Item
                        name="strategy"
                        label="接口唯一标识"
                        rules={[{required: true, message: '请选择接口唯一标识'}]}
                        labelCol={{span: 4}}
                        wrapperCol={{span: 20, offset: 1}}
                        colon={false}
                    >
                        <Select
                            allowClear
                            options={[
                                {label: 'Method & Path', value: 'methodAndPath'},
                                {label: 'OperationId', value: 'operationId'},
                                {label: 'Query 参数', value: 'query'},
                                {label: 'Body 参数', value: 'body'},
                                {label: 'Header 参数', value: 'header'},
                            ]}
                        />
                    </Form.Item>
                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) => prevValues.strategy !== currentValues.strategy}
                    >
                        {({getFieldValue}) => (getFieldValue('strategy') === 'query' ? (
                            <Form.Item
                                colon={false}
                                name="customizeParameter"
                                label="Query 参数"
                                labelCol={{span: 4}}
                                wrapperCol={{span: 20, offset: 1}}
                                rules={[{required: true, message: '请填写字段名称'}]}
                            >
                                <Select
                                    mode="tags"
                                    placeholder="参数名，如：action"
                                    maxTagCount="responsive"
                                />
                            </Form.Item>
                        ) : getFieldValue('strategy') === 'body' ? (
                            <Form.Item
                                label="Body 参数"
                                labelCol={{span: 4}}
                                wrapperCol={{span: 20, offset: 1}}
                                colon={false}
                                style={{marginBottom: 0}}
                            >
                                <Form.Item
                                    name="bodyType"
                                    rules={[{required: true, message: '请选择Body类型'}]}
                                    style={{display: 'inline-block', width: 'calc(50% - 8px)'}}
                                >
                                    <Select
                                        placeholder="123123"
                                        options={[
                                            {label: 'multipart/form-data', value: 'formdata'},
                                            {label: 'application/x-www-form-urlencoded', value: 'urlencoded'},
                                        ]}
                                    />
                                </Form.Item>
                                <Form.Item
                                    name="customizeParameter"
                                    style={{display: 'inline-block', width: 'calc(50% - 8px)', margin: '0 8px'}}
                                    rules={[{required: true, message: '请填写字段名称'}]}
                                >
                                    <Select
                                        mode="tags"
                                        placeholder="参数名，如：action"
                                        maxTagCount="responsive"
                                    />
                                </Form.Item>
                            </Form.Item>
                        ) : getFieldValue('strategy') === 'header' ? (
                            <Form.Item
                                colon={false}
                                name="customizeParameter"
                                label="Header 参数"
                                labelCol={{span: 4}}
                                wrapperCol={{span: 20, offset: 1}}
                                rules={[{required: true, message: '请填写字段名称'}]}
                            >
                                <Select
                                    mode="tags"
                                    placeholder="参数名，如：action"
                                    maxTagCount="responsive"
                                />
                            </Form.Item>
                        ) : null)
                        }
                    </Form.Item>
                    <Form.Item label={null} style={{marginBottom: 20}}>
                        <Flex justify="flex-end">
                            <Button onClick={modalToggleOpen} style={{marginRight: 12}}>取消</Button>
                            <Button type="primary" htmlType="submit" loading={updateLoading}>
                                确定
                            </Button>
                        </Flex>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};
