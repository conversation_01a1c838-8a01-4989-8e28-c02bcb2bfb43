import {createMappedRegion, createRegion} from 'region-core';
import {isEmpty, sortBy} from 'lodash';
import {apiGetFile, apiPostFetchFileVersions} from '@/api/icode/file';
import {FileNode, RefType} from '@/types/icode/repo';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';

const forceUpdateFileTreeRegion = createRegion(0);

export const useForceUpdateFileTree = forceUpdateFileTreeRegion.useValue;

const forceUpdateFileTree = () => forceUpdateFileTreeRegion.set(v => v + 1);

interface TypeParams {
    repo: string;
    type: RefType;
    commit: string;
    path: string;
    forceUpdate?: boolean;
    withFileSize?: boolean;
    isDataset?: boolean;
}

interface Key {
    repo: string;
    commit: string;
    path: string;
}

const fileNodeRegion = createMappedRegion<Key, FileNode>();

export const useFileNodeError = fileNodeRegion.useError;

export const useFileNode = fileNodeRegion.useValue;

export const getFileNode = fileNodeRegion.getValue;

export const resetAllFileNode = fileNodeRegion.resetAll;

const order = ['TREE', 'BLOB'];

const getFile = async ({repo, commit, type, path, withFileSize, isDataset}: TypeParams) => {
    const result = await apiGetFile({repo, value: commit, path, type, hasLog: true, aliasFolder: true, withFileSize});
    if (!isDataset) {
        return result;
    }

    const {childs = []} = result;
    // 根据文件列表获取其最新版本
    const filePaths = childs.filter(child => child.type === 'BLOB').map(child => child.path);
    const params = {
        repo,
        paths: filePaths,
    };

    if (filePaths.length === 0) {
        return result;
    }

    const fileVersionResult = await apiPostFetchFileVersions(params);
    result.childs = childs.map(child => {
        child.fileVersion = fileVersionResult[child.path] ?? '';
        return child;
    });

    return result;
};

const loadFileNodeInternal = fileNodeRegion.loadBy(
    ({repo, commit, path}) => ({repo, commit, path}),
    getFile,
    (node, result) => {
        // 当前文件
        const {childs, parent} = result;
        const sortedChildren = sortBy(childs, ({type}) => order.indexOf(type));
        const nextNode: FileNode = node ? {...node} : parent;
        nextNode.children = sortedChildren;
        return nextNode;
    }
);

export const loadFileNode = async (params: TypeParams) => {
    await loadFileNodeInternal(params);
    const {repo, commit, path} = params;
    const file = getFileNode({repo, commit, path});
    if (file && file.children) {
        file.children.forEach(child => {
            fileNodeRegion.set({repo, commit, path: child.path}, node => (node ? {...node} : child));
        });
    }
    forceUpdateFileTree();
};

export const useCurrentFileNode = (): FileNode | undefined => {
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const filePath = useCurrentPath();
    const node = useFileNode({repo: repoName, commit: refName, path: filePath});
    return node;
};

interface RemoveDatasetCurrentFileChildrenNodeParams {
    repo: string;
    commit: string;
    parentPath: string;
    childrenPath: string;
}

export const removeCurrentFileChildrenNode = ({
    repo,
    commit,
    parentPath,
    childrenPath,
}: RemoveDatasetCurrentFileChildrenNodeParams) => {
    fileNodeRegion.set(
        {repo, commit, path: parentPath},
        (node: FileNode | undefined) => {
            const children = node?.children ?? [];
            if (isEmpty(children)) {
                return node;
            }
            return {
                ...node,
                children: children.filter(item => item.path !== childrenPath),
            };
        }
    );
};
