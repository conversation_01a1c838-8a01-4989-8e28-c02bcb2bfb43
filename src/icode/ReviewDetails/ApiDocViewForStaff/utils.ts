import {ApiDocByTaskId} from '@/api/icode/apidoc';
import {
    ApiTreeNode,
    ParsedApiDetail,
    GroupedApiData,
    ContentCollection,
} from './types';

/**
 * 按catalog对原始数据进行分组
 */
export const groupDataByCatalog = (apiData: ApiDocByTaskId[]): GroupedApiData => {
    return apiData.reduce((groups, api) => {
        const catalog = api.catalog || '未分类';
        if (!groups[catalog]) {
            groups[catalog] = [];
        }
        groups[catalog].push(api);
        return groups;
    }, {} as GroupedApiData);
};

/**
 * 清理JSON字符串中的控制字符 - 使用强力修复方法
 */
const cleanJsonString = (jsonStr: string): string => {
    let cleaned = jsonStr;

    // 1. 移除BOM字符
    cleaned = cleaned.replace(/^\ufeff/, '');

    // 2. 统一换行符格式
    cleaned = cleaned
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .replace(/\u2028/g, '\n')
        .replace(/\u2029/g, '\n');

    // 3. 强力修复：专门处理 "data":"{...}" 这种嵌套JSON模式
    // 这是导致解析错误的主要原因
    cleaned = cleaned.replace(
        /"(data|example|content)"\s*:\s*"(\{[^}]*\})"/g,
        (match, fieldName, jsonContent) => {
            // 对嵌套的JSON内容进行完整转义
            const escaped = jsonContent
                .replace(/\\/g, '\\\\') // 转义反斜杠
                .replace(/"/g, '\\"') // 转义双引号
                .replace(/\n/g, '\\n') // 转义换行符
                .replace(/\t/g, '\\t') // 转义制表符
                .replace(/\r/g, '\\r'); // 转义回车符

            return `"${fieldName}": "${escaped}"`;
        }
    );

    // 4. 更复杂的嵌套JSON处理：多层花括号
    // 处理 "data":"{ "nested": { "deep": "value" } }" 这种情况
    let attempts = 0;
    const maxAttempts = 5; // 避免无限循环

    while (attempts < maxAttempts) {
        const before = cleaned;

        // 使用递归的方法处理嵌套的花括号
        cleaned = cleaned.replace(
            /"(data|example|content)"\s*:\s*"(\{(?:[^{}"]|"[^"]*")*\})"/g,
            (match, fieldName, jsonContent) => {
                const escaped = jsonContent
                    .replace(/\\/g, '\\\\')
                    .replace(/"/g, '\\"')
                    .replace(/\n/g, '\\n')
                    .replace(/\t/g, '\\t')
                    .replace(/\r/g, '\\r');

                return `"${fieldName}": "${escaped}"`;
            }
        );

        // 如果没有更多变化，退出循环
        if (cleaned === before) {
            break;
        }
        attempts++;
    }

    // 5. 最后兜底：处理任何剩余的未转义换行符
    cleaned = cleaned.replace(
        /"([^"\\]*(?:\\.[^"\\]*)*)"/g,
        (match, content) => {
            if (content.includes('\n') || content.includes('\t') || content.includes('\r')) {
                const fixed = content
                    .replace(/([^\\])\n/g, '$1\\n')
                    .replace(/([^\\])\t/g, '$1\\t')
                    .replace(/([^\\])\r/g, '$1\\r')
                    .replace(/^(\n)/g, '\\n')
                    .replace(/^(\t)/g, '\\t')
                    .replace(/^(\r)/g, '\\r');
                return `"${fixed}"`;
            }
            return match;
        }
    );

    return cleaned;
};

/**
 * 解析content字段中的JSON数据，提取API详情
 */
export const parseApiContent = (content: string): ContentCollection | null => {
    if (!content || typeof content !== 'string') {
        console.warn('parseApiContent: content为空或非字符串类型');
        return null;
    }

    try {
        // 清理控制字符后再解析
        const cleanedContent = cleanJsonString(content);
        const parsed = JSON.parse(cleanedContent);

        // content字段包含一个数组，第一个元素包含完整的集合信息
        if (Array.isArray(parsed) && parsed[0]) {
            return parsed[0] as ContentCollection;
        }

        console.warn('parseApiContent: 解析结果不是预期的数组格式');
        return null;
    } catch (error) {
        // 提供更详细的错误信息
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('解析API content失败:', {
            error: errorMessage,
            contentLength: content.length,
            contentPreview: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
            // 查找错误位置附近的内容
            ...(errorMessage.includes('position') && (() => {
                const match = /position (\d+)/.exec(errorMessage);
                if (match) {
                    const position = parseInt(match[1], 10);
                    const start = Math.max(0, position - 50);
                    const end = Math.min(content.length, position + 50);
                    return {
                        errorPosition: position,
                        contextAroundError: content.substring(start, end),
                    };
                }
                return {};
            })()),
        });
        return null;
    }
};

/**
 * 安全解析JSON字符串
 */
const safeJsonParse = (jsonStr: string, fallback: any = undefined): any => {
    try {
        return JSON.parse(jsonStr);
    } catch {
        return fallback;
    }
};

/**
 * 从原始API数据中提取详细信息 - 完整版本
 */
// eslint-disable-next-line complexity
export const extractApiDetails = (apiData: ApiDocByTaskId): ParsedApiDetail | null => {
    if (!apiData.content || !apiData.apiParseId) {
        return null;
    }

    const contentCollection = parseApiContent(apiData.content);
    if (!contentCollection?.httpCollection || !Array.isArray(contentCollection.httpCollection)) {
        return null;
    }

    // 递归搜索函数，能够在嵌套的children结构中查找API
    const findApiRecursively = (collections: any[]): any => {
        for (const collection of collections) {
            // 在当前层级的items中查找
            if (collection.items && Array.isArray(collection.items)) {
                const found = collection.items.find((item: any) =>
                    item.name === apiData.apiName
                    && item.method?.toLowerCase() === apiData.apiMethod?.toLowerCase()
                    && (item.path || '').replace(/^\//, '') === (apiData.apiPath || '').replace(/^\//, '')
                );
                if (found) {
                    return found;
                }
            }

            // 递归搜索children
            if (collection.children && Array.isArray(collection.children)) {
                const found = findApiRecursively(collection.children);
                if (found) {
                    return found;
                }
            }
        }
        return null;
    };

    // 在httpCollection中查找匹配的API
    const targetApi = findApiRecursively(contentCollection.httpCollection);

    if (!targetApi) {
        return null;
    }

    // 完整提取所有字段
    const parsedDetail: ParsedApiDetail = {
        apiParseId: apiData.apiParseId,
        apiName: apiData.apiName || targetApi.name || '',
        apiMethod: apiData.apiMethod || targetApi.method || '',
        apiPath: apiData.apiPath || targetApi.path || '',
        catalog: apiData.catalog || '未分类',
        description: targetApi.description,
        tags: targetApi.tags || [],

        // 请求参数相关 - 完整提取
        parameters: {
            path: targetApi.parameters?.path || [],
            query: targetApi.parameters?.query || [],
            header: targetApi.parameters?.header || [],
            cookie: targetApi.parameters?.cookie || [],
        },
        commonParameters: targetApi.commonParameters || {},

        // 请求体相关 - 完整提取
        requestBody: targetApi.requestBody ? {
            type: targetApi.requestBody.type || 'application/json',
            jsonSchema: targetApi.requestBody.jsonSchema,
            example: targetApi.requestBody.example || undefined,
            parameters: targetApi.requestBody.parameters || [],
        } : undefined,

        // 响应相关 - 完整提取
        responses: targetApi.responses?.map((response: any) => ({
            id: response.id,
            name: response.name,
            code: response.code,
            contentType: response.contentType,
            jsonSchema: response.jsonSchema,
        })) || [],

        responseExamples: targetApi.responseExamples?.map((example: any) => ({
            name: example.name,
            data: example.data,
            responseId: example.responseId,
        })) || [],

        // 测试用例相关 - 完整提取
        cases: targetApi.cases?.map((testCase: any) => ({
            type: testCase.type,
            name: testCase.name,
            parameters: {
                path: testCase.parameters?.path || [],
                query: testCase.parameters?.query || [],
                cookie: testCase.parameters?.cookie || [],
                header: testCase.parameters?.header || [],
            },
            requestBody: testCase.requestBody ? {
                parameters: testCase.requestBody.parameters || [],
                data: testCase.requestBody.data,
                type: testCase.requestBody.type,
                generateMode: testCase.requestBody.generateMode,
            } : undefined,
            responseId: testCase.responseId,
        })) || [],

        // 自定义字段 - 完整提取
        customApiFields: targetApi.customApiFields
            ? safeJsonParse(targetApi.customApiFields, targetApi.customApiFields)
            : undefined,

        // 保留原始数据作为备用
        rawApiData: targetApi,
        // 保存状态相关字段
        status: apiData.status,
        savedByUser: apiData.savedByUser,
        updatedTime: apiData.updatedTime,
        iapiUrl: apiData.iapiUrl,
    };
    return parsedDetail;
};

/**
 * 将原始数据转换为树形结构
 */
export const transformToTreeData = (apiData: ApiDocByTaskId[]): ApiTreeNode[] => {
    const groupedData = groupDataByCatalog(apiData);
    const treeNodes: ApiTreeNode[] = [];

    Object.entries(groupedData).forEach(([catalog, apis]) => {
        // 创建catalog节点
        const catalogNode: ApiTreeNode = {
            key: `catalog-${catalog}`,
            title: catalog,
            type: 'catalog',
            catalog,
            children: [],
            isLeaf: false,
        };

        // 为每个API创建子节点
        apis.forEach(api => {
            if (api.apiParseId) {
                const apiNode: ApiTreeNode = {
                    key: `api-${api.apiParseId}`,
                    title: api.apiName || '未命名接口',
                    type: 'api',
                    catalog,
                    apiParseId: api.apiParseId,
                    apiName: api.apiName,
                    apiMethod: api.apiMethod,
                    apiPath: api.apiPath,
                    content: api.content,
                    swaggerContent: api.swaggerContent,
                    isLeaf: true,
                    // 传递保存状态相关字段
                    status: api.status,
                    savedByUser: api.savedByUser,
                    updatedTime: api.updatedTime,
                    iapiUrl: api.iapiUrl,
                };
                catalogNode.children!.push(apiNode);
            }
        });

        // 只有有子节点的catalog才添加到树中
        if (catalogNode.children!.length > 0) {
            treeNodes.push(catalogNode);
        }
    });

    return treeNodes;
};

/**
 * 根据catalog获取其下所有未保存过的API的ID列表
 */
export const getApiIdsByCatalog = (treeData: ApiTreeNode[], catalog: string): number[] => {
    const catalogNode = treeData.find(node =>
        node.type === 'catalog' && node.catalog === catalog
    );

    if (!catalogNode || !catalogNode.children) {
        return [];
    }

    return catalogNode.children
        .filter(child => child.apiParseId && child.status !== 'ACCEPTED')
        .map(child => child.apiParseId!);
};

/**
 * 检查指定catalog下的所有API是否都被选中
 */
export const isCatalogFullySelected = (
    treeData: ApiTreeNode[],
    catalog: string,
    selectedApiIds: number[]
): boolean => {
    const apiIds = getApiIdsByCatalog(treeData, catalog);
    return apiIds.length > 0 && apiIds.every(id => selectedApiIds.includes(id));
};

/**
 * 检查指定catalog下是否有部分API被选中
 */
export const isCatalogPartiallySelected = (
    treeData: ApiTreeNode[],
    catalog: string,
    selectedApiIds: number[]
): boolean => {
    const apiIds = getApiIdsByCatalog(treeData, catalog);
    return apiIds.some(id => selectedApiIds.includes(id))
           && !apiIds.every(id => selectedApiIds.includes(id));
};

/**
 * 根据API ID查找对应的树节点
 */
export const findApiNodeById = (treeData: ApiTreeNode[], apiId: number): ApiTreeNode | null => {
    for (const catalogNode of treeData) {
        if (catalogNode.children) {
            const apiNode = catalogNode.children.find(child => child.apiParseId === apiId);
            if (apiNode) {
                return apiNode;
            }
        }
    }
    return null;
};

/**
 * 获取所有API的ID列表
 */
export const getAllApiIds = (treeData: ApiTreeNode[]): number[] => {
    const apiIds: number[] = [];
    treeData.forEach(catalogNode => {
        if (catalogNode.children) {
            catalogNode.children.forEach(apiNode => {
                if (apiNode.apiParseId) {
                    apiIds.push(apiNode.apiParseId);
                }
            });
        }
    });
    return apiIds;
};

/**
 * 格式化API方法显示
 */
export const formatApiMethod = (method: string): string => {
    return method?.toUpperCase() || 'GET';
};

/**
 * 生成API显示标题
 */
export const generateApiTitle = (apiName: string): string => {
    return apiName || '未命名接口';
};

/**
 * 获取API详细信息的摘要统计
 */
export const getApiDetailSummary = (detail: ParsedApiDetail): {
    hasRequestParams: boolean;
    hasRequestBody: boolean;
    responseCount: number;
    testCaseCount: number;
    hasCustomFields: boolean;
} => {
    return {
        hasRequestParams: Boolean(
            detail.parameters?.path?.length
            || detail.parameters?.query?.length
            || detail.parameters?.header?.length
            || detail.parameters?.cookie?.length
        ),
        hasRequestBody: Boolean(detail.requestBody),
        responseCount: detail.responses?.length || 0,
        testCaseCount: detail.cases?.length || 0,
        hasCustomFields: Boolean(detail.customApiFields),
    };
};

/**
 * 从 ContentCollection 中提取 dataSchemaCollection 作为 definitions
 */
export const extractDefinitionsFromContent = (content: string): Record<string, any> => {
    const contentCollection = parseApiContent(content);
    if (!contentCollection?.dataSchemaCollection || !Array.isArray(contentCollection.dataSchemaCollection)) {
        return {};
    }

    const definitions: Record<string, any> = {};

    // 遍历外层数组 (例如，"Schemas" 分组)
    contentCollection.dataSchemaCollection.forEach((schemaGroup: any) => {
        // 确保分组中有 items 数组
        if (schemaGroup.items && Array.isArray(schemaGroup.items)) {
            // 遍历 items 数组获取真正的 schema 定义
            schemaGroup.items.forEach((schema: any) => {
                if (schema.name && schema.jsonSchema) {
                    definitions[schema.name] = schema.jsonSchema;
                }
            });
        }
    });

    return definitions;
};

/**
 * 获取 API 详情包含 definitions 的完整信息
 */
export const extractApiDetailsWithDefinitions = (apiData: ApiDocByTaskId): {
    apiDetail: ParsedApiDetail | null;
    definitions: Record<string, any>;
} => {
    const apiDetail = extractApiDetails(apiData);
    const definitions = extractDefinitionsFromContent(apiData.content || '');

    return {
        apiDetail,
        definitions,
    };
};
