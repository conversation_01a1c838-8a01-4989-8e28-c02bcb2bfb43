/* eslint-disable max-len */
import {useState, useEffect, useMemo, useCallback, FC} from 'react';
import styled from '@emotion/styled';
import {useSupportAreaFull} from '@/regions/staff/supportArea';
import {ApiDocViewForStaffProps, ApiTreeNode} from './types';
import {transformToTreeData, extractApiDetailsWithDefinitions, getAllApiIds} from './utils';
import ApiTreeList from './ApiTreeList';
import ApiDetailView from './ApiDetailView';

const MainSection = styled.div`
    flex:1;
    overflow: auto;
    position: relative;
`;

const ApiDocViewForStaff: FC<ApiDocViewForStaffProps> = ({
    apiData,
    onSelectionChange,
    onApiClick,
    selectionState,
    setSelectionState,
}) => {
    // 状态管理
    const supportAreaFull = useSupportAreaFull();
    const [treeData, setTreeData] = useState<ApiTreeNode[]>([]);

    // 当前选中的API详情和Definitions
    const currentApiData = useMemo(
        () => {
            if (!selectionState.currentApiId) {
                return {apiDetail: null, definitions: {}};
            }

            const selectedApi = apiData.find(api => api.apiParseId === selectionState.currentApiId);
            if (!selectedApi) {
                return {apiDetail: null, definitions: {}};
            }

            return extractApiDetailsWithDefinitions(selectedApi);
        },
        [selectionState.currentApiId, apiData]
    );

    // 处理数据变化 - 当apiData改变时重新转换数据
    useEffect(
        () => {
            if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
                setTreeData([]);
                setSelectionState(prev => ({
                    ...prev,
                    selectedApiIds: [],
                    currentApiId: null,
                    expandedCatalogs: [],
                }));
                return;
            }

            // 转换为树形数据
            const treeNodes = transformToTreeData(apiData);
            setTreeData(treeNodes);

            // 初始化展开状态（展开所有catalog）
            const allCatalogs = treeNodes.map(node => node.catalog).filter(Boolean) as string[];
            setSelectionState(prev => ({
                ...prev,
                expandedCatalogs: allCatalogs,
            }));

            // 如果有数据，默认选中第一个API
            const firstApi = treeNodes[0]?.children?.[0];
            if (firstApi?.apiParseId) {
                setSelectionState(prev => ({
                    ...prev,
                    currentApiId: firstApi.apiParseId,
                }));
            }
        },
        [apiData, setSelectionState]
    );

    // 选择状态变化回调 - 转换为SelectedApiInfo数组
    useEffect(
        () => {
            const selectedApis = selectionState.selectedApiIds.map(apiId => {
                const originalApi = apiData.find(api => api.apiParseId === apiId);
                if (!originalApi) {
                    return null;
                }
                return {
                    taskId: originalApi.taskId,
                    apiParseId: originalApi.apiParseId,
                    accepted: true,
                    swaggerContent: originalApi.swaggerContent,
                };
            }).filter((api): api is NonNullable<typeof api> => api !== null);

            onSelectionChange?.(selectedApis);
        },
        [selectionState.selectedApiIds, onSelectionChange, apiData]
    );

    // API点击回调
    useEffect(
        () => {
            if (currentApiData.apiDetail) {
                onApiClick?.(currentApiData.apiDetail);
            }
        },
        [currentApiData.apiDetail, onApiClick]
    );

    // 处理选择变化
    const handleSelectionChange = useCallback(
        (newSelectedIds: number[]) => {
            setSelectionState(prev => ({
                ...prev,
                selectedApiIds: newSelectedIds,
            }));
        },
        [setSelectionState]
    );

    // 处理API点击
    const handleApiClick = useCallback(
        (apiId: number) => {
            setSelectionState(prev => ({
                ...prev,
                currentApiId: apiId,
            }));
        },
        [setSelectionState]
    );

    // 处理catalog展开/折叠
    const handleCatalogExpand = useCallback(
        (catalog: string, expanded: boolean) => {
            setSelectionState(prev => ({
                ...prev,
                expandedCatalogs: expanded
                    ? [...prev.expandedCatalogs, catalog]
                    : prev.expandedCatalogs.filter(c => c !== catalog),
            }));
        },
        [setSelectionState]
    );

    // 处理全选
    const handleSelectAll = useCallback(
        (checked: boolean) => {
            const allApiIds = getAllApiIds(treeData);
            setSelectionState(prev => ({
                ...prev,
                selectedApiIds: checked ? allApiIds : [],
            }));
        },
        [setSelectionState, treeData]
    );

    // 计算全选状态
    const allApiIds = useMemo(
        () => getAllApiIds(treeData),
        [treeData]
    );
    const isAllSelected = useMemo(
        () => {
            return allApiIds.length > 0 && allApiIds.every(id => selectionState.selectedApiIds.includes(id));
        },
        [allApiIds, selectionState.selectedApiIds]
    );

    const isIndeterminate = useMemo(
        () => {
            return selectionState.selectedApiIds.length > 0 && !isAllSelected;
        },
        [selectionState.selectedApiIds.length, isAllSelected]
    );

    if (treeData.length === 0) {
        return (
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '40vh',
                    borderBottom: '1px solid #EBEBEB',
                }}
            >
                暂无API文档数据
            </div>
        );
    }

    return (
        <div
            style={{
                display: 'flex',
                borderBottom: '1px solid #EBEBEB',
            }}
        >
            {/* 左侧树形列表 */}
            <MainSection style={{borderRight: '1px solid #EBEBEB', height: supportAreaFull ? 'calc(100vh - 310px)' : 'calc(100vh - 360px)', maxWidth: 220, paddingRight: 12}}>
                <ApiTreeList
                    treeData={treeData}
                    selectedApiIds={selectionState.selectedApiIds}
                    currentApiId={selectionState.currentApiId}
                    expandedCatalogs={selectionState.expandedCatalogs}
                    isAllSelected={isAllSelected}
                    isIndeterminate={isIndeterminate}
                    onSelectionChange={handleSelectionChange}
                    onApiClick={handleApiClick}
                    onCatalogExpand={handleCatalogExpand}
                    onSelectAll={handleSelectAll}
                />
            </MainSection>

            {/* 右侧详情展示 */}
            <MainSection>
                <ApiDetailView
                    apiDetail={currentApiData.apiDetail}
                    definitions={currentApiData.definitions}
                />
            </MainSection>
        </div>
    );
};

export default ApiDocViewForStaff;
