import {Dispatch, SetStateAction} from 'react';
import {ApiDocByTaskId} from '@/api/icode/apidoc';
import {HttpApiRequestBodyType} from '@/api/icode/apidoc';

// API树形节点类型
export interface ApiTreeNode {
    key: string;
    title: string;
    type: 'catalog' | 'api';
    catalog?: string;
    apiParseId?: number;
    apiName?: string;
    apiMethod?: string;
    apiPath?: string;
    children?: ApiTreeNode[];
    content?: string;
    swaggerContent?: string;
    isLeaf?: boolean;
    // 保存状态相关字段
    status?: string;
    savedByUser?: string;
    updatedTime?: string;
    iapiUrl?: string;
}

// 解析后的API详细信息
export interface ParsedApiDetail {
    apiParseId: number;
    apiName: string;
    apiMethod: string;
    apiPath: string;
    catalog: string;
    description?: string;
    tags?: string[];
    // 请求参数相关
    parameters?: {
        path?: any[];
        query?: any[];
        header?: any[];
        cookie?: any[];
    };
    commonParameters?: any;
    // 请求体相关 - 修复类型定义
    requestBody?: {
        type: HttpApiRequestBodyType;
        jsonSchema?: any;
        example?: any;
        parameters?: any[];
    };
    // 响应相关
    responses?: Array<{
        id: string;
        name: string;
        code: number;
        contentType: string;
        jsonSchema?: any;
    }>;
    responseExamples?: Array<{
        name: string;
        data: string;
        responseId: string;
    }>;
    // 测试用例相关
    cases?: Array<{
        type: string;
        name: string;
        parameters?: {
            path?: any[];
            query?: any[];
            cookie?: any[];
            header?: any[];
        };
        requestBody?: {
            parameters?: any[];
            data?: string;
            type?: string;
            generateMode?: string;
        };
        responseId?: string;
    }>;
    // 自定义字段
    customApiFields?: string | any;
    // 原始完整数据（备用）
    rawApiData?: any;
    // 保存状态相关字段
    status?: string;
    savedByUser?: string;
    updatedTime?: string;
    iapiUrl?: string;
}

// 选中API的保存信息接口
export interface SelectedApiInfo {
    taskId?: number;
    apiParseId: number;
    accepted: boolean;
    swaggerContent: string;
}

// 组件Props接口
export interface ApiDocViewForStaffProps {
    apiData: ApiDocByTaskId[];
    onSelectionChange?: (selectedApis: SelectedApiInfo[]) => void;
    onApiClick?: (apiDetail: ParsedApiDetail) => void;
    selectionState?: SelectionState;
    setSelectionState?: Dispatch<SetStateAction<SelectionState>>;
}

// 选择状态类型
export interface SelectionState {
    selectedApiIds: number[];
    currentApiId: number | null;
    expandedCatalogs: string[];
}

// 原始数据分组类型
export interface GroupedApiData {
    [catalog: string]: ApiDocByTaskId[];
}

// content中的完整集合结构
export interface ContentCollection {
    Project?: string;
    httpCollection?: Array<{
        name: string;
        children?: any[];
        items?: any[];
    }>;
    socketCollection?: any[];
    docCollection?: any[];
    dataSchemaCollection?: Array<{ // 更精确的类型定义
        name: string;
        jsonSchema: any;
        description?: string;
    }>;
    apiTestCaseCollection?: any[];
    environments?: any[];
    hasFullPath?: boolean;
    contentMimeTypes?: any[];
}

// ApiDetailView组件Props扩展
export interface ApiDetailViewProps {
    apiDetail: ParsedApiDetail | null;
    loading?: boolean;
    definitions?: Record<string, any>; // 新增：Schema definitions支持
}
