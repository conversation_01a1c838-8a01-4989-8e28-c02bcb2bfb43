import {useMemo, useState, useRef, useEffect} from 'react';
import {Empty} from 'antd';
import moment from 'moment';
import {
    containerStyles,
    apiHeaderStyles,
    apiInfoStyles,
    apiNameStyles,
    apiPathStyles,
    methodTagStyles,
} from './styles';
import {ApiDetailViewProps} from './types';
import MethodTag from './MethodTag';
import RequestParametersSection from './RequestParametersSection';
import ResponseSection from './ResponseSection';

const ApiDetailView = ({
    apiDetail,
    loading = false,
    definitions = {},
}: ApiDetailViewProps) => {
    // 双卡模式状态和宽度监听
    const [cardMode, setCardMode] = useState<'single' | 'dual'>('single');
    const containerRef = useRef<HTMLDivElement>(null);

    // 监听容器宽度变化，统一判断双卡模式
    useEffect(
        () => {
            const updateCardMode = () => {
                if (containerRef.current) {
                    const width = containerRef.current.clientWidth;

                    // 双卡模式判断逻辑：
                    // 开启阈值：700px
                    // 关闭阈值：650px
                    // 滞后区间：650-700px保持当前状态
                    if (width > 700 && cardMode === 'single') {
                        setCardMode('dual');
                    } else if (width < 650 && cardMode === 'dual') {
                        setCardMode('single');
                    }
                }
            };

            // 初始检查
            const timer = setTimeout(updateCardMode, 100);

            // ResizeObserver监听容器变化
            const resizeObserver = new ResizeObserver(() => {
                updateCardMode();
            });

            if (containerRef.current) {
                resizeObserver.observe(containerRef.current);
            }

            // 窗口resize事件作为后备
            const handleResize = () => {
                setTimeout(updateCardMode, 50);
            };
            window.addEventListener('resize', handleResize);

            return () => {
                clearTimeout(timer);
                resizeObserver.disconnect();
                window.removeEventListener('resize', handleResize);
            };
        },
        [cardMode]
    );

    // 适配响应数据格式
    const adaptedResponseData = useMemo(
        () => {
            if (!apiDetail) {
                return null;
            }

            const responses = apiDetail.responses?.map(response => ({
                id: response.id,
                name: response.name,
                code: response.code,
                contentType: response.contentType,
                jsonSchema: response.jsonSchema,
            })) || [];

            const responseExamples = apiDetail.responseExamples?.map(example => ({
                name: example.name,
                data: example.data,
                responseId: example.responseId,
            })) || [];

            return {
                responses,
                responseExamples,
            };
        },
        [apiDetail]
    );

    // 加载状态
    if (loading) {
        return (
            <div className={containerStyles}>
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '200px',
                    color: '#666',
                }}
                >
                    加载API详情中...
                </div>
            </div>
        );
    }

    // 空状态
    if (!apiDetail) {
        return (
            <div className={containerStyles}>
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '300px',
                }}
                >
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="请选择一个API查看详情"
                    />
                </div>
            </div>
        );
    }

    return (
        <div ref={containerRef} className={containerStyles}>
            {/* API标题部分 */}
            <div className={apiHeaderStyles}>
                <div style={{display: 'flex', flexDirection: 'row', gap: '8px', alignItems: 'center'}}>
                    <MethodTag
                        method={apiDetail.apiMethod}
                        type="solid"
                        className={methodTagStyles}
                    />
                    <div className={apiInfoStyles}>
                        <div className={apiNameStyles}>
                            {apiDetail.apiName}
                        </div>
                        <div className={apiPathStyles}>
                            {apiDetail.apiPath}
                        </div>
                    </div>
                </div>
                {/* 第二行：保存信息 */}
                {apiDetail.status === 'ACCEPTED' && apiDetail.savedByUser && apiDetail.updatedTime && (
                    <div style={{
                        fontSize: '12px',
                        color: '#666',
                        marginTop: '4px',
                        lineHeight: '16px',
                    }}
                    >
                        {apiDetail.savedByUser} 保存于 {moment(apiDetail.updatedTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                )}
            </div>

            {/* 请求参数部分 */}
            <RequestParametersSection
                parameters={apiDetail.parameters}
                requestBody={apiDetail.requestBody}
                definitions={definitions}
                mode={cardMode} // 传递双卡模式
            />

            {/* 响应部分 */}
            {adaptedResponseData && (
                <ResponseSection
                    responses={adaptedResponseData.responses}
                    responseExamples={adaptedResponseData.responseExamples}
                    definitions={definitions}
                    mode={cardMode} // 传递双卡模式
                />
            )}
        </div>
    );
};

export default ApiDetailView;
