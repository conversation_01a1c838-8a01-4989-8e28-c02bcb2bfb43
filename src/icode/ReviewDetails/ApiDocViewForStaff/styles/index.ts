/**
 * ApiDocViewForStaff 统一样式系统入口
 *
 * 本文件作为样式系统的统一入口，提供：
 * 1. 主题配置的重新导出
 * 2. 各模块样式的统一导出
 * 3. 向后兼容的别名导出
 * 4. 便捷的组合样式工具
 */

// === 主题系统导出 ===
export {
    // 主题配置
    theme,
    colors,
    typography,
    spacing,
    borderRadius,
    shadows,
    transitions,
    breakpoints,

    // 工具函数
    utils,
    animations,
    media,
} from './theme';

// === 布局样式导出 ===
export {
    // 主要布局容器
    mainContainer,
    leftPanel,
    rightPanel,
    divider,

    // 内容容器
    treeContainer,
    treeHeader,
    treeFooter,
    detailContainer,
    detailCard,
    summaryBar,

    // 响应式样式
    responsive,

    // 容器变体
    containerVariants,

    // 状态样式
    stateStyles,
} from './layout';

// === 通用组件样式导出 ===
export {
    // API头部
    apiHeader,

    // Method Tag
    methodTag,

    // 章节
    section,

    // 代码查看器
    codeViewer,

    // 响应状态码
    responseCode,

    // 参数卡片
    parameterCard,

    // Tab组件
    tabs,

    // 空状态
    emptyState,

    // 树形组件
    tree,
} from './components';

// === 向后兼容的别名导出 ===
// 为了保持与现有代码的兼容性，提供一些常用样式的别名

import {css} from '@emotion/css';

// 从主题系统中导入，用于构建兼容性样式
import {colors as themeColors, spacing as themeSpacing, borderRadius} from './theme';

// 为了兼容现有的 components/styles.ts 中的导出
export const COLORS = {
    text: {
        primary: themeColors.text.primary,
        secondary: themeColors.text.secondary,
        white: themeColors.text.white,
    },
    background: {
        white: themeColors.background.primary,
        light: themeColors.background.secondary,
    },
    border: {
        light: themeColors.border.light,
        default: themeColors.border.default,
    },
} as const;

export const TYPOGRAPHY = {
    title: {
        fontSize: '16px',
        fontWeight: 500,
        color: themeColors.text.primary,
    },
    subtitle: {
        fontSize: '14px',
        fontWeight: 500,
        color: themeColors.text.primary,
    },
    path: {
        fontSize: '12px',
        fontWeight: 400,
        color: themeColors.text.secondary,
    },
    body: {
        fontSize: '13px',
        fontWeight: 400,
        color: themeColors.text.secondary,
    },
} as const;

// 兼容性样式导出
export const containerStyles = css`
    padding: 16px 14px 20px 20px;
    height: 100%;
    overflow: auto;
    background: ${themeColors.background.primary};
`;

export const apiHeaderStyles = css`
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: ${themeSpacing.xs};
    margin-bottom: ${themeSpacing.xxl};
`;

export const apiInfoStyles = css`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: ${themeSpacing.sm};
`;

export const apiNameStyles = css`
    font-size: 16px;
    font-weight: 500;
    color: ${themeColors.text.primary};
    line-height: 1.5;
`;

export const apiPathStyles = css`
    font-size: 12px;
    font-weight: 400;
    color: ${themeColors.text.secondary};
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
`;

export const sectionStyles = css`
    margin-bottom: ${themeSpacing.xxl};
`;

export const sectionTitleStyles = css`
    font-size: 14px;
    font-weight: 500;
    color: ${themeColors.text.primary};
    margin-bottom: ${themeSpacing.sm};
    margin-top: ${themeSpacing.xxl};
    line-height: 1.5;
`;

export const emptyStateStyles = css`
    display: flex;
    align-items: center;
    justify-content: center;
    height: 120px;
    color: ${themeColors.text.secondary};
    font-size: 13px;
    background: ${themeColors.background.secondary};
    border-radius: ${borderRadius.sm};
    border: 1px dashed ${themeColors.border.default};
`;

export const codeViewerStyles = css`
    .ant-typography {
        margin-bottom: 0;
    }
    
    pre {
        background: ${themeColors.background.secondary};
        border: 1px solid ${themeColors.border.light};
        border-radius: ${borderRadius.sm};
        padding: ${themeSpacing.md};
        font-size: 12px;
        line-height: 1.6;
        overflow-x: auto;
        max-height: 300px;
        margin: 0;
    }
`;

export const responseCodeStyles = css`
    .status-2xx {
        color: ${themeColors.success};
        font-weight: 500;
    }
    
    .status-4xx {
        color: ${themeColors.warning};
        font-weight: 500;
    }
    
    .status-5xx {
        color: ${themeColors.error};
        font-weight: 500;
    }
    
    .status-default {
        color: ${themeColors.text.secondary};
        font-weight: 500;
    }
`;

export const methodTagStyles = css`
    margin-right: 0;
    flex-shrink: 0;
`;

// 从原 components/styles.ts 移植的样式（为了向后兼容）
export const responsiveParameterContainer = css`
    display: flex;
    gap: ${themeSpacing.lg};
    align-items: stretch;
`;

export const dualCardContainer = css`
    min-width: 500px !important;
    display: flex;
    flex-direction: column;
`;

export const singleCardStyles = css`
    border: 1px solid ${themeColors.border.light};
    border-radius: ${borderRadius.lg};
    overflow: hidden;
    
    .card-content {
        padding: ${themeSpacing.lg};
        background: ${themeColors.background.primary};
    }
`;

export const newEmptyStateStyles = css`
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 200px;
    color: ${themeColors.text.disabled};
    font-size: 13px;
    background: ${themeColors.background.light};
    border-radius: ${borderRadius.sm};
`;

export const customTabsStyles = css`
    width: 100%;  // 自适应宽度
    min-width: 480px;  // 保证最小宽度为480px
    min-height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: ${themeSpacing.xl};
    
    .ant-tabs,
    .ant-5-tabs {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        min-height: 0 !important;
    }
    
    .ant-5-tabs-nav {
        background: #E8E8E84D !important;
        height: 32px !important;
        margin-bottom: 0 !important;
        border-radius: ${borderRadius.lg} ${borderRadius.lg} 0 0 !important;
        display: flex !important;
        align-items: center !important;
        border-bottom: none !important;
    }
    
    .ant-5-tabs-nav-wrap {
        padding: 0 !important;
    }
    
    .ant-5-tabs-nav-list {
        height: 32px !important;
        display: flex !important;
        align-items: center !important;
    }
    
    .ant-5-tabs-tab {
        height: 32px !important;
        padding: 8px 16px 4px 16px !important;
        margin: 0 !important;
        margin-left: 0 !important;
        border: none !important;
        background: transparent !important;
        border-radius: ${borderRadius.lg} ${borderRadius.lg} 0 0 !important;
        font-size: 12px !important;
        line-height: 24px !important;
        font-weight: 500 !important;
        color: ${themeColors.text.disabled} !important;
        position: relative !important;
        
        &.ant-5-tabs-tab-active {
            background: linear-gradient(
                90deg, 
                ${themeColors.background.gradient.tab} 0%, 
                ${themeColors.background.gradient.tabSecondary} 100%
            ) !important;
            
            .ant-5-tabs-tab-btn {
                color: transparent !important;
                background: ${themeColors.text.gradient} !important;
                -webkit-background-clip: text !important;
                -webkit-text-fill-color: transparent !important;
                background-clip: text !important;
                font-size: 12px !important;
                font-weight: 500 !important;
            }
        }
        
        & + .ant-5-tabs-tab {
            margin-left: 0 !important;
        }
    }
    
    .ant-5-tabs-tab-btn {
        font-size: 12px !important;
        line-height: 24px !important;
    }
    
    .ant-5-tabs-ink-bar {
        display: none !important;
    }
    
    .ant-5-tabs-content-holder {
        padding: 8px 16px 16px 12px !important;
        border-top: none !important;
        border-radius: 0 0 ${borderRadius.lg} ${borderRadius.lg} !important;
        background: ${themeColors.background.light} !important;
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
    }
    
    .ant-5-tabs-content {
        height: 100% !important;
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
    }
    
    .ant-5-tabs-tabpane {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .ant-5-tabs-tabpane-active {
        height: 100% !important;
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        min-height: 0 !important;
    }
`;

export const responseSectionTabsStyles = css`
    .ant-5-tabs-nav {
        margin-bottom: ${themeSpacing.md};
    }

    .ant-5-tabs-tab {
        padding: ${themeSpacing.sm} 0;
        font-size: 12px !important;
        font-weight: 400 !important;
        color: ${themeColors.text.secondary} !important;
    }

    .ant-5-tabs-tab-active .ant-5-tabs-tab-btn {
        color: transparent !important;
        background: ${themeColors.text.gradient} !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        font-size: 12px !important;
        font-weight: 500 !important;
    }

    .ant-5-tabs-ink-bar {
        background: ${themeColors.text.gradient};
    }
`;

// 便捷工具函数
export const createResponsiveStyle = (mobileStyle: string, tabletStyle?: string, desktopStyle?: string) => css`
    ${desktopStyle || ''}
    
    @media (max-width: 1024px) {
        ${tabletStyle || ''}
    }
    
    @media (max-width: 768px) {
        ${mobileStyle}
    }
`;

export const createHoverStyle = (baseStyle: string, hoverStyle: string) => css`
    ${baseStyle}
    
    &:hover {
        ${hoverStyle}
    }
`;

// 默认导出主题对象以保持兼容性
export {theme as default} from './theme';
