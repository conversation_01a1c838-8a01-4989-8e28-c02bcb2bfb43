import {forwardRef} from 'react';
import {Checkbox, CheckboxProps, CheckboxRef} from 'antd';
import styled from '@emotion/styled';

// 使用styled-components针对Antd v5的Checkbox进行样式定制
const StyledCheckbox = styled(Checkbox)`
    /* 针对Antd v5的checkbox结构 */
    .ant-5-checkbox {
        width: 12px !important;
        height: 12px !important;
        min-width: 12px !important;
        min-height: 12px !important;
    }

    .ant-5-checkbox-disabled {
        background-color: #eaeaea !important;
    }
    
    .ant-5-checkbox .ant-5-checkbox-inner,
    .ant-5-checkbox-inner {
        width: 12px !important;
        height: 12px !important;
        min-width: 12px !important;
        min-height: 12px !important;
        border-radius: 4px !important;
        box-sizing: border-box !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    /* 调整勾选标记大小 */
    .ant-5-checkbox-checked .ant-5-checkbox-inner::after,
    &.ant-5-checkbox-wrapper-checked .ant-5-checkbox .ant-5-checkbox-inner::after {
        width: 4px !important;
        height: 7px !important;
        border-width: 0 1.5px 1.5px 0 !important;
        border-color: #ffffff !important;
        transform: rotate(45deg) scale(0.8) !important;
        left: 4px !important;
        top: 2px !important;
    }
    
    /* 未选中状态 */
    .ant-5-checkbox .ant-5-checkbox-inner,
    .ant-5-checkbox-inner {
        border-color: #d9d9d9 !important;
        background-color: transparent !important;
    }
    
    /* 选中状态 */
    .ant-5-checkbox-checked .ant-5-checkbox-inner,
    &.ant-5-checkbox-wrapper-checked .ant-5-checkbox .ant-5-checkbox-inner {
        background-color: #3F00D3 !important;
        border-color: #3F00D3 !important;
    }
    
    /* 半选中状态 */
    .ant-5-checkbox-indeterminate .ant-5-checkbox-inner,
    &.ant-5-checkbox-wrapper-indeterminate .ant-5-checkbox .ant-5-checkbox-inner {
        background-color: #3F00D3 !important;
        border-color: #3F00D3 !important;
    }
    
    /* 调整半选状态标记（横线） */
    .ant-5-checkbox-indeterminate .ant-5-checkbox-inner::after,
    &.ant-5-checkbox-wrapper-indeterminate .ant-5-checkbox .ant-5-checkbox-inner::after {
        width: 6px !important;
        height: 1.5px !important;
        background-color: #ffffff !important;
        border: none !important;
        transform: none !important;
        left: 3px !important;
        top: 5.25px !important;
        border-radius: 0 !important;
        content: '' !important;
        position: absolute !important;
    }
    
    /* 移除hover效果 */
    &:hover .ant-5-checkbox-inner,
    .ant-5-checkbox:hover .ant-5-checkbox-inner {
        border-color: #d9d9d9 !important;
    }
    
    &:hover .ant-5-checkbox-checked .ant-5-checkbox-inner,
    &:hover .ant-5-checkbox-indeterminate .ant-5-checkbox-inner {
        border-color: #3F00D3 !important;
    }
    
    /* 通用选择器作为备用 */
    input[type="checkbox"] + span {
        width: 12px !important;
        height: 12px !important;
        min-width: 12px !important;
        min-height: 12px !important;
        border-radius: 4px !important;
        box-sizing: border-box !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    input[type="checkbox"]:checked + span {
        background-color: #3F00D3 !important;
        border-color: #3F00D3 !important;
    }
    
    input[type="checkbox"]:checked + span::after {
        width: 4px !important;
        height: 7px !important;
        border-width: 0 1.5px 1.5px 0 !important;
        border-color: #ffffff !important;
        transform: rotate(45deg) scale(0.8) !important;
        left: 4px !important;
        top: 1px !important;
        position: absolute !important;
        content: '' !important;
    }
    
    input[type="checkbox"]:indeterminate + span {
        background-color: #3F00D3 !important;
        border-color: #3F00D3 !important;
    }
    
    input[type="checkbox"]:indeterminate + span::after {
        width: 6px !important;
        height: 1.5px !important;
        background-color: #ffffff !important;
        border: none !important;
        transform: none !important;
        left: 3px !important;
        top: 5.25px !important;
        border-radius: 0 !important;
        content: '' !important;
        position: absolute !important;
    }
`;

interface CustomCheckboxProps extends CheckboxProps {
    className?: string;
}

export const CustomCheckbox = forwardRef<CheckboxRef, CustomCheckboxProps>(
    (props, ref) => {
        return <StyledCheckbox ref={ref} {...props} />;
    }
);

CustomCheckbox.displayName = 'CustomCheckbox';

export default CustomCheckbox;
