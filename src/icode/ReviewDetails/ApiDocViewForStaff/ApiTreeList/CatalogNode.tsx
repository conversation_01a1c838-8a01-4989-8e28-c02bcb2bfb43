import {Row, Col} from 'antd';
import {RightOutlined} from '@ant-design/icons';
import {ApiTreeNode} from '../types';
import {
    isCatalogFullySelected,
    isCatalogPartiallySelected,
} from '../utils';
import CustomCheckbox from '../CustomCheckbox';
import {
    catalogContainer,
    catalogHeader,
    catalogContent,
    catalogTitle,
    apiListContainer,
    apiListExpanded,
    apiListCollapsed,
    getCatalogExpandIconStyles,
} from './styles';
import ApiNode from './ApiNode';

interface CatalogNodeProps {
    catalogNode: ApiTreeNode;
    treeData: ApiTreeNode[];
    selectedApiIds: number[];
    currentApiId: number | null;
    expandedCatalogs: string[];
    onCatalogToggle: (catalog: string) => void;
    onCatalogCheck: (catalog: string, checked: boolean) => void;
    onApiClick: (apiId: number) => void;
    onApiCheck: (apiId: number, checked: boolean) => void;
}

const CatalogNode = ({
    catalogNode,
    treeData,
    selectedApiIds,
    currentApiId,
    expandedCatalogs,
    onCatalogToggle,
    onCatalogCheck,
    onApiClick,
    onApiCheck,
}: CatalogNodeProps) => {
    const catalog = catalogNode.catalog!;
    const isExpanded = expandedCatalogs.includes(catalog);
    const isCatalogSelected = isCatalogFullySelected(treeData, catalog, selectedApiIds);
    const isCatalogIndeterminate = isCatalogPartiallySelected(treeData, catalog, selectedApiIds);

    return (
        <div key={catalogNode.key} className={catalogContainer}>
            {/* Catalog头部 */}
            <Row
                align="middle"
                className={catalogHeader}
            >
                <Col className={catalogContent}>
                    {/* 展开/折叠图标 */}
                    <span
                        onClick={() => onCatalogToggle(catalog)}
                        className={getCatalogExpandIconStyles(isExpanded)}
                    >
                        <RightOutlined />
                    </span>
                    {/* Catalog复选框 */}
                    <CustomCheckbox
                        disabled={catalogNode.children.every(item => item.status === 'ACCEPTED')}
                        checked={isCatalogSelected}
                        indeterminate={isCatalogIndeterminate}
                        onChange={e => {
                            onCatalogCheck(catalog, e.target.checked);
                        }}
                    >
                        {/* Catalog名称 */}
                        <span className={catalogTitle}>
                            {catalogNode.title}
                        </span>
                    </CustomCheckbox>
                </Col>
            </Row>

            {/* API列表 */}
            {catalogNode.children && (
                <div
                    className={`${apiListContainer} ${isExpanded ? apiListExpanded : apiListCollapsed}`}
                >
                    {catalogNode.children.map(apiNode => {
                        const isSelected = apiNode.apiParseId ? selectedApiIds.includes(apiNode.apiParseId) : false;
                        const isCurrent = apiNode.apiParseId === currentApiId;
                        return (
                            <ApiNode
                                key={apiNode.key}
                                apiNode={apiNode}
                                isSelected={isSelected}
                                isCurrent={isCurrent}
                                onApiClick={onApiClick}
                                onApiCheck={onApiCheck}
                            />
                        );
                    })}
                </div>
            )}
        </div>
    );
};

export default CatalogNode;
