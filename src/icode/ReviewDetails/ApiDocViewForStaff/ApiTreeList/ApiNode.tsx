/* eslint-disable max-len */
import {Row, Col, Tooltip} from 'antd';
import {LinkOutlined} from '@ant-design/icons';
import {ApiTreeNode} from '../types';
import {formatApiMethod} from '../utils';
import CustomCheckbox from '../CustomCheckbox';
import MethodTag from '../MethodTag';
import {
    apiContent,
    methodTag,
    apiName,
    apiNameText,
    getApiNodeStyles,
} from './styles';

interface ApiNodeProps {
    apiNode: ApiTreeNode;
    isSelected: boolean;
    isCurrent: boolean;
    onApiClick: (apiId: number) => void;
    onApiCheck: (apiId: number, checked: boolean) => void;
}

const ApiNode = ({
    apiNode,
    isSelected,
    isCurrent,
    onApiClick,
    onApiCheck,
}: ApiNodeProps) => {
    return (
        <Row
            key={apiNode.key}
            align="middle"
            className={getApiNodeStyles(isCurrent)}
            onClick={() => {
                if (apiNode.apiParseId) {
                    onApiClick(apiNode.apiParseId);
                }
            }}
        >
            <Col className={apiContent}>
                {/* API复选框 */}
                <CustomCheckbox
                    checked={isSelected}
                    disabled={apiNode.status === 'ACCEPTED'}
                    onChange={e => {
                        e.stopPropagation();
                        if (apiNode.apiParseId) {
                            onApiCheck(apiNode.apiParseId, e.target.checked);
                        }
                    }}
                    style={{marginRight: '8px'}}
                />

                {/* HTTP方法标签 */}
                <MethodTag
                    method={formatApiMethod(apiNode.apiMethod || '')}
                    type="outline"
                    className={methodTag}
                />

                {/* API名称 */}
                <span className={apiName}>
                    <span className={apiNameText}>
                        {apiNode.apiName}
                    </span>
                    {/* 显示链接图标（当API已保存时） */}
                    {apiNode.status === 'ACCEPTED' && apiNode.iapiUrl && (
                        <Tooltip title={'该接口已保存，可点击跳转至接口详情页面。'}>
                            <LinkOutlined
                                style={{
                                    marginLeft: '6px',
                                    color: '#1890ff',
                                    cursor: 'pointer',
                                }}
                                onClick={() => {window.open(apiNode.iapiUrl, '_blank');}}
                            />
                        </Tooltip>
                    )}
                </span>
            </Col>
        </Row>
    );
};

export default ApiNode;
