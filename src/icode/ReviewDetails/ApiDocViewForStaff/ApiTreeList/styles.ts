import {css} from '@emotion/css';
import {colors, spacing, borderRadius} from '../styles/theme';

/**
 * ApiTreeList 组件专用样式
 * 提取自原有的内联样式定义，使用统一主题配置
 */

// 目录容器
export const catalogContainer = css`
    margin-bottom: ${spacing.sm};
`;

// API列表容器基础样式
export const apiListContainer = css`
    margin-left: ${spacing.sm};
    margin-top: ${spacing.xs};
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.2s ease;
`;

// API列表折叠状态
export const apiListCollapsed = css`
    max-height: 0;
    opacity: 0;
`;

// API列表展开状态
export const apiListExpanded = css`
    max-height: 1000px;
    opacity: 1;
`;

// 目录头部样式
export const catalogHeader = css`
    cursor: pointer;
    transition: background-color 0.2s;
`;

// 目录内容样式
export const catalogContent = css`
    display: flex;
    align-items: center;
`;

// 目录展开图标基础样式
export const catalogExpandIcon = css`
    margin-right: ${spacing.sm};
    font-size: 12px;
    transition: transform 0.2s ease;
    cursor: pointer;
`;

// 目录展开图标展开状态
export const catalogExpandIconExpanded = css`
    transform: rotate(90deg);
`;

// 目录标题样式
export const catalogTitle = css`
    font-size: 14px;
    color: ${colors.text.primary};
    font-weight: 500;
`;

// API节点基础样式
export const apiNode = css`
    padding: 2px ${spacing.md} 2px 44px;
    margin-bottom: 2px;
    cursor: pointer;
    border-radius: ${borderRadius.sm};
    background-color: transparent;
    border: 1px solid transparent;
    transition: all 0.2s;
    
    &:hover {
        background-color: ${colors.background.hover};
    }
`;

// API节点当前选中状态
export const apiNodeCurrent = css`
    background-color: #F2F2F2;
    
    &:hover {
        background-color: #F2F2F2;
    }
`;

// API内容样式
export const apiContent = css`
    display: flex;
    align-items: center;
`;

// Method Tag样式
export const methodTag = css`
    margin-right: ${spacing.xs};
`;

// API名称样式
export const apiName = css`
    font-size: 14px;
    color: ${colors.text.primary};
    font-weight: normal;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    overflow: hidden;
`;

// API名称文本部分样式（处理溢出）
export const apiNameText = css`
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 0 1 auto;
    min-width: 0;
`;

// 组合样式工具函数
export const getApiListStyles = (isExpanded: boolean) => {
    return `${apiListContainer} ${isExpanded ? apiListExpanded : apiListCollapsed}`;
};

export const getCatalogExpandIconStyles = (isExpanded: boolean) => {
    return `${catalogExpandIcon} ${isExpanded ? catalogExpandIconExpanded : ''}`;
};

export const getApiNodeStyles = (isCurrent: boolean) => {
    return `${apiNode} ${isCurrent ? apiNodeCurrent : ''}`;
};
