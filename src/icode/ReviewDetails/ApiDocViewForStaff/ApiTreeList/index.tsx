import {useCallback} from 'react';
import {ApiTreeNode} from '../types';
import {getApiIdsByCatalog} from '../utils';
import CatalogNode from './CatalogNode';

interface ApiTreeListProps {
    treeData: ApiTreeNode[];
    selectedApiIds: number[];
    currentApiId: number | null;
    expandedCatalogs: string[];
    isAllSelected: boolean;
    isIndeterminate: boolean;
    onSelectionChange: (selectedApiIds: number[]) => void;
    onApiClick: (apiId: number) => void;
    onCatalogExpand: (catalog: string, expanded: boolean) => void;
    onSelectAll: (checked: boolean) => void;
}

const ApiTreeList = ({
    treeData,
    selectedApiIds,
    currentApiId,
    expandedCatalogs,
    onSelectionChange,
    onApiClick,
    onCatalogExpand,
}: ApiTreeListProps) => {
    // 处理单个API选择
    const handleApiCheck = useCallback(
        (apiId: number, checked: boolean) => {
            if (checked) {
                onSelectionChange([...selectedApiIds, apiId]);
            } else {
                onSelectionChange(selectedApiIds.filter(id => id !== apiId));
            }
        },
        [selectedApiIds, onSelectionChange]
    );

    // 处理catalog全选
    const handleCatalogCheck = useCallback(
        (catalog: string, checked: boolean) => {
            const catalogApiIds = getApiIdsByCatalog(treeData, catalog);
            if (checked) {
                // 选中catalog下所有API
                const newSelectedIds = [...selectedApiIds];
                catalogApiIds.forEach(id => {
                    if (!newSelectedIds.includes(id)) {
                        newSelectedIds.push(id);
                    }
                });
                onSelectionChange(newSelectedIds);
            } else {
                // 取消选中catalog下所有API
                onSelectionChange(selectedApiIds.filter(id => !catalogApiIds.includes(id)));
            }
        },
        [treeData, selectedApiIds, onSelectionChange]
    );

    // 处理catalog展开/折叠
    const handleCatalogToggle = useCallback(
        (catalog: string) => {
            const isExpanded = expandedCatalogs.includes(catalog);
            onCatalogExpand(catalog, !isExpanded);
        },
        [expandedCatalogs, onCatalogExpand]
    );

    return (
        <div>
            {treeData.map(catalogNode => (
                <CatalogNode
                    key={catalogNode.key}
                    catalogNode={catalogNode}
                    treeData={treeData}
                    selectedApiIds={selectedApiIds}
                    currentApiId={currentApiId}
                    expandedCatalogs={expandedCatalogs}
                    onCatalogToggle={handleCatalogToggle}
                    onCatalogCheck={handleCatalogCheck}
                    onApiClick={onApiClick}
                    onApiCheck={handleApiCheck}
                />
            ))}
        </div>
    );
};

export default ApiTreeList;
