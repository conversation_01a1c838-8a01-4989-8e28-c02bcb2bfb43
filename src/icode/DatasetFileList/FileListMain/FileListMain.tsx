/**
 * @file 文件列表页
 */
import {useCallback, useEffect, useMemo} from 'react';
import {Divider, Tabs} from 'antd';
import type {TabsProps} from 'antd';
import {useNavigate, useParams} from 'react-router-dom';
import {Button, marginLeft, marginRight} from '@panda-design/components';
import {cx} from '@emotion/css';
import {CloseOutlined} from '@ant-design/icons';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useFileNode, useFileNodeError} from '@/regions/icode/fileTree';
import {
    loadDatasetLinkModel,
    loadDatasetLinkReport,
    loadDatasetLinkTag,
} from '@/regions/icode/dataset';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {useCurrentPath} from '@/hooks/icode/current/useCurrentPath';
import {
    pushOpenedFileNodes,
    useCloseFile,
    useOpenedFileNodes,
} from '@/regions/dataset/openedFileNodes';
import {getFileUrl} from '@/utils/icode/route/getFileUrl';
import {getDatasetFileIcon} from '@/utils/dataset/fils';
import {ProjectFilesLink} from '@/links/comatestack';
import FileDetailMain from '../FileDetailMain';
import FileListPage from '../FileListPage';
import {FileListMainContainer, TabName} from './FileListMainStyle';

export const FileListMain = () => {
    const repoName = useCurrentRepoName();
    const refName = useCurrentRefName();
    const {projectUuid} = useParams();
    const filePath = useCurrentPath() as string;
    const error = useFileNodeError({repo: repoName, commit: refName, path: filePath});
    const currentNode = useFileNode({repo: repoName, commit: refName, path: filePath});
    const fileNodes = useOpenedFileNodes({repoName, refName});
    const navigate = useNavigate();
    const {closeFile} = useCloseFile();

    useEffect(
        () => {
            loadDatasetLinkModel({repoName});
            loadDatasetLinkReport({repoName});
            loadDatasetLinkTag({repoName});
        },
        [repoName]
    );

    useEffect(
        () => {
            const findNode = fileNodes.find(item => item.path === currentNode?.path);
            if (!findNode && currentNode && currentNode.type === 'BLOB') {
                pushOpenedFileNodes({repoName, refName}, currentNode);
            }
        },
        [currentNode, fileNodes, refName, repoName]
    );

    const tabItems: TabsProps['items'] = useMemo(
        () => {
            return fileNodes.map((item, index) => {
                const {name = '', path} = item;
                const newName = path ? name.split('/').pop() : '根目录';
                const selectedNodeIndex = fileNodes.findIndex(item => item.path === filePath);
                const Icon = getDatasetFileIcon(name);
                return {
                    key: path,
                    label: (
                        <>
                            <TabName ellipsis={{tooltip: true}}>{newName}</TabName>
                            <Button
                                className={cx(path === filePath ? marginRight(12) : 'tab-close-button')}
                                size="small"
                                type="text"
                                icon={<CloseOutlined />}
                                onClick={e => {
                                    e.stopPropagation();
                                    closeFile(path);
                                }}
                            />
                            <Divider
                                className={cx(marginLeft(0), marginRight(0))}
                                type="vertical"
                                // 当前选中的和其左侧的第一个，不显示分割线
                                style={{
                                    visibility: path === filePath || index === selectedNodeIndex - 1
                                        ? 'hidden'
                                        : 'visible',
                                }}
                            />
                        </>
                    ),
                    children: <FileDetailMain fileNode={item} />,
                    icon: <Icon style={{fontSize: '16px'}} />,
                    forceRender: true,
                };
            });
        },
        [closeFile, fileNodes, filePath]
    );

    const handleChangeTab = useCallback(
        (activeKey: string) => {
            const findNode = fileNodes.find(item => item.path === activeKey);
            const url = getFileUrl({
                repoName,
                type: findNode?.type?.toLowerCase(),
                encodedRefName: encodeURIComponent(refName),
                path: activeKey,
            });

            navigate(url);
        },
        [fileNodes, navigate, refName, repoName]
    );

    const mainContent = useMemo(
        () => {
            return (
                <FileListMainContainer hasItems={tabItems.length > 0} isOpenedTree={currentNode?.type === 'TREE'}>
                    <Tabs
                        activeKey={filePath}
                        items={tabItems}
                        hideAdd
                        onChange={handleChangeTab}
                        animated={false}
                    />
                </FileListMainContainer>
            );
        },
        [currentNode, filePath, handleChangeTab, tabItems]
    );

    // 统一跳转到根目录
    if (error) {
        navigate(ProjectFilesLink.toUrl({projectUuid}));
        return;
    }

    return (
        <>
            {mainContent}
            {/* 文件夹信息 */}
            {
                currentNode?.type === 'TREE' && <FileListPage />
            }
        </>
    );
};
