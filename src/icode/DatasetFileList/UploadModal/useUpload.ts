/* eslint-disable max-len */
import {useCallback, useState, useRef, useEffect} from 'react';
import CryptoJS from 'crypto-js';
import {message as Message} from '@panda-design/components';
import {useBoolean} from 'huse';
import {get} from 'lodash';
import {
    apiPostSmallLfsFileUpload,
    apiPostUploadFromBOS,
    apiPostInitPartUpload,
    apiPostProcessPartUpload,
    apiPostCompletePartUpload,
    apiPostCommitPartUpload,
    apiPostCancelPartUpload,
    apiPostAddLinkTag,
    apiPostUpdateFileMetaData,
} from '@/api/icode/dataset';
import {getParentPath, splitPath} from '@/utils/icode/files';
import {loadFileNode} from '@/regions/icode/fileTree';
import {loadDatasetFileMetaData, updateDatasetLinkTag, useDatasetLinkTag} from '@/regions/icode/dataset';
import {useCurrentBranchType} from '@/hooks/icode/current/useCurrentBranchType';
import {useCurrentRepoName} from '@/regions/icode/currentRepo';
import {useCurrentRefName} from '@/hooks/icode/current/useCurrentRefName';
import {CommitPartUploadFileInfo, FileMetaTargetType, TrainScene, TrainTarget} from '@/types/icode/dataset';
import {setUploadProgress} from '@/regions/icode/dataset/fileUploadRegion';
import {useIsDataset} from '@/providers/icode/IsDatasetProvider';

const PART_FILE_SIZE = 1024 * 1024 * 15; // 15MB
const SMALL_FILE_SIZE = 1024 * 1024 * 10; // 10M
const MAX_UPLOAD_CONCURRENT = 3;

export interface UploadFileProps {
    path: string;
    message: string;
    trainScene: string;
    trainTarget: string;
    url?: string;
    file?: File;
    tags?: string[];
    sha256?: string;
}

interface UploadBigFileProps {
    file: File;
    uploadId: string;
    sha256: string;
    path: string;
}

interface CompletePartUploadProps {
    sha256: string;
    message: string;
    uploadId: string;
    path: string;
    size: number;
    tags: string[];
    trainScene: string;
    trainTarget: string;
}

interface CommitPartUploadProps {
    message: string;
    files: CommitPartUploadFileInfo[];
    tags: string[];
    trainScene: string;
    trainTarget: string;
}


const useUpload = () => {
    const [uploadId, setUploadId] = useState<string>();
    const [sha256, setShe256] = useState<string>();
    const repo = useCurrentRepoName();
    const branch = useCurrentRefName();
    const branchType = useCurrentBranchType();
    const [loading, {on: showUploadLoading, off: hideUploadLoading}] = useBoolean(false);
    const [cancelLoading, {on: showCancelLoading, off: hideCancelLoading}] = useBoolean(false);
    const sha256Ref = useRef(false);
    const uploadRef = useRef(false);
    const linkTagList = useDatasetLinkTag({repoName: repo});
    const isDataset = useIsDataset();

    const updateFileTags = useCallback(
        async (tags: string[], path: string, trainScene: string, trainTarget: string) => {
            if (!tags && !trainScene && !trainTarget) {
                return;
            }
            const tagIds: number[] = [];
            const {fileName} = splitPath(path);
            if (tags) {
                const existingTagSet = new Set(linkTagList.map(item => {
                    if (tags.includes(item.name)) {
                        tagIds.push(item.id);
                    }
                    return item.name;
                }));

                const newTags = tags.filter(tag => !existingTagSet.has(tag));

                for (const newTag of newTags) {
                    try {
                        // 新增标签
                        const tag = await apiPostAddLinkTag({
                            repoName: repo,
                            content: newTag,
                            id: null,
                        });
                        // 更新标签列表状态值
                        updateDatasetLinkTag({
                            repoName: repo,
                            content: newTag,
                            name: newTag,
                            id: tag.id,
                        });
                        tagIds.push(tag.id);
                    }
                    catch (error) {
                        Message.error(`新标签${newTag}添加失败`);
                        console.error('Error adding tag:', newTag, error);
                    }
                }
            }

            const paramsTarget = {
                type: 'FILE' as FileMetaTargetType,
                path,
                trainScene: [trainScene as TrainScene],
                trainTarget: [trainTarget as TrainTarget],
                model: [] as any[],
                linkReport: [] as any[],
                tag: tagIds,
            };
            try {
                // 给新文件打标签
                await apiPostUpdateFileMetaData({repoName: repo, branch, target: [paramsTarget]});
            }
            catch (error) {
                Message.error(`给${fileName}打标签失败`);
            }

        },
        [branch, linkTagList, repo]
    );

    const refetchFileTree = useCallback(
        (path: string) => {
            const {dir} = splitPath(path);
            loadFileNode({repo, commit: branch, type: branchType, path: dir, forceUpdate: true, withFileSize: true, isDataset});

            // parentPath为null时，是根目录，也需要loadFileNode
            const parentPath = getParentPath(dir);
            loadFileNode({repo, commit: branch, type: branchType, path: parentPath, forceUpdate: true, withFileSize: true, isDataset});

            loadDatasetFileMetaData({
                repoName: repo,
                commit: branch,
                target: [{path: null, type: 'DS'}, {path: dir, type: 'DIR'}],
            });
        },
        [branch, branchType, isDataset, repo]
    );

    const cancelPartUpload = useCallback(
        async (currentSha256?: string, currentUploadId?: string) => {
            sha256Ref.current = false;
            uploadRef.current = false;
            const querySha256 = currentSha256 || sha256;
            const queryUploadId = currentUploadId || uploadId;
            if (querySha256 && queryUploadId) {
                showCancelLoading();
                try {
                    await apiPostCancelPartUpload({uploadId: queryUploadId, sha256: querySha256});
                    hideCancelLoading();
                    hideUploadLoading();
                    setShe256('');
                    setUploadId('');
                }
                catch (error) {
                    hideUploadLoading();
                    hideCancelLoading();
                    console.error(error.message);
                }
            }
            else {
                hideUploadLoading();
            }
        },
        [uploadId, sha256, showCancelLoading, hideCancelLoading, hideUploadLoading]
    );

    const calculateSHA256 = useCallback(
        async (file: File) => {
            showUploadLoading();
            sha256Ref.current = true;
            let count = 0;
            const bufferChunkSize = 1024 * 1024; // 1MB
            const hash = CryptoJS.algo.SHA256.create();
            return new Promise<string>(async resolve => {
                const reader = new FileReader();
                // 递归读取文件的每个分块
                function processChunk(offset: number) {
                    const start = offset;
                    const end = Math.min(start + bufferChunkSize, file?.size);
                    count = end;
                    // 以流的方式读取文件
                    const sliceFile = file.slice(start, end);
                    reader.readAsArrayBuffer(sliceFile);
                }
                // 当读取完整个文件后，计算哈希值并返回
                reader.onloadend = () => {
                    const arrayBuffer = reader.result as unknown as number[];
                    const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer);

                    // 更新哈希对象
                    hash.update(wordArray);
                    if (sha256Ref.current) {
                        if (count < file.size) {
                            // 继续处理下一个分块
                            processChunk(count);
                        }
                        else {
                            // 计算哈希值并返回
                            const sha256Hash = hash.finalize();
                            resolve(sha256Hash.toString());
                        }
                    }
                };
                // 开始处理文件内容分块
                processChunk(0);
            });
        },
        [showUploadLoading]
    );

    const uploadSmallFile = useCallback(
        async ({path, message, file, trainScene, trainTarget, tags, sha256}: UploadFileProps) => {
            setUploadProgress({key: path, progress: 0});
            if (file) {
                try {
                    await apiPostSmallLfsFileUpload({
                        sha256,
                        repo,
                        branch,
                        path,
                        message,
                        file,
                        trainScene,
                        trainTarget,
                    });
                    setUploadProgress({key: path, progress: 100});
                    hideUploadLoading();
                    await updateFileTags(tags, path, trainScene, trainTarget);
                    refetchFileTree(path ?? '');
                }
                catch (error) {
                    hideUploadLoading();
                    setUploadProgress({key: path, progress: -1});
                    console.error(error.message);
                }
            }
        },
        [branch, hideUploadLoading, refetchFileTree, repo, updateFileTags]
    );

    const uploadBosUrl = useCallback(
        async ({path, message, trainScene, trainTarget, url, tags}: UploadFileProps) => {
            setUploadProgress({key: path, progress: 0});
            showUploadLoading();
            try {
                await apiPostUploadFromBOS({
                    repo,
                    branch,
                    path: path ? path : '',
                    commitMsg: message,
                    trainScene,
                    trainTarget,
                    url: url,
                });
                hideUploadLoading();
                setUploadProgress({key: path, progress: 100});
                hideUploadLoading();
                await updateFileTags(tags, path, trainScene, trainTarget);
                refetchFileTree(path ?? '');
            }
            catch (error) {
                setUploadProgress({key: path, progress: -1});
                hideUploadLoading();
                console.error(error.message);
            }
        },
        [branch, hideUploadLoading, refetchFileTree, repo, showUploadLoading, updateFileTags]
    );

    const createUploadPartTask = useCallback(
        (
            uploadId: string,
            file: File,
            sha256: string,
            path: string,
            completedPartsRef: {current: number},
            failedPartsRef: {current: Set<number>},
            allPartNum: number,
            onError: (error: any) => void
        ) => {
            const updateProgress = () => {
                const progressNum = Math.min(Math.ceil((completedPartsRef.current / allPartNum) * 100), 98);
                setUploadProgress({key: path, progress: progressNum});
            };

            return async (partNum: number, retryCount = 0): Promise<void> => {
                if (!uploadRef.current || partNum > allPartNum) {
                    return;
                }

                const start = (partNum - 1) * PART_FILE_SIZE;
                const end = Math.min(start + PART_FILE_SIZE, file.size);
                const sliceFile = file.slice(start, end);

                try {
                    await apiPostProcessPartUpload({
                        sha256,
                        uploadId,
                        currentPartNum: partNum,
                        file: sliceFile as File,
                    });
                    completedPartsRef.current++;
                    failedPartsRef.current.delete(partNum);
                    updateProgress();
                } catch (error) {
                    console.error(`Part ${partNum} upload failed (attempt ${retryCount + 1}):`, error);

                    if (retryCount < 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                        return createUploadPartTask(uploadId, file, sha256, path, completedPartsRef, failedPartsRef, allPartNum, onError)(partNum, retryCount + 1);
                    }

                    failedPartsRef.current.add(partNum);
                    onError(error);
                }
            };
        },
        []
    );

    const uploadBigFile = useCallback(
        async ({uploadId, file, sha256, path}: UploadBigFileProps, onError: (error: any) => void) => {
            const allPartNum = Math.ceil(file.size / PART_FILE_SIZE);
            uploadRef.current = true;

            const completedPartsRef = {current: 0};
            const failedPartsRef = {current: new Set<number>()};

            // 处理分片上传错误的回调
            const handlePartError = (error: any) => {
                uploadRef.current = false;
                onError(error);
            };

            const uploadPart = createUploadPartTask(uploadId, file, sha256, path, completedPartsRef, failedPartsRef, allPartNum, handlePartError);

            try {
                // 最多 MAX_UPLOAD_CONCURRENT 个请求在进行中
                let currentPartNum = 1;
                const runningTasks = new Set<Promise<void>>();

                const startTask = async (partNum: number) => {
                    if (!uploadRef.current) {
                        return;
                    }

                    const task = uploadPart(partNum).finally(async () => {
                        runningTasks.delete(task);
                        if (currentPartNum <= allPartNum && uploadRef.current) {
                            await new Promise(resolve => setTimeout(resolve, 50));
                            startTask(currentPartNum++);
                        }
                    });
                    runningTasks.add(task);
                };

                // 启动初始并发任务
                const maxInitialTasks = Math.min(MAX_UPLOAD_CONCURRENT, allPartNum);
                for (let i = 0; i < maxInitialTasks; i++) {
                    startTask(currentPartNum++);
                }

                // 等待所有任务完成
                while (runningTasks.size > 0 && uploadRef.current) {
                    await Promise.race(runningTasks);
                }

            } catch (error) {
                onError(error);
            }
        },
        [createUploadPartTask]
    );

    const commitPartUpload = useCallback(
        async ({message, files, trainScene, trainTarget, tags}: CommitPartUploadProps) => {
            const path = get(files, '[0].path', '');
            try {
                await apiPostCommitPartUpload({
                    repo,
                    branch,
                    message,
                    files,
                    trainScene,
                    trainTarget,
                });
                hideUploadLoading();
                setUploadProgress({key: path, progress: 100});
                await updateFileTags(tags, path, trainScene, trainTarget);
                refetchFileTree(path ?? '');
            }
            catch (error) {
                hideUploadLoading();
                setUploadProgress({key: path, progress: -1});
                console.error(error.message);
            }
        },
        [branch, hideUploadLoading, refetchFileTree, repo, updateFileTags]
    );

    const completePartUpload = useCallback(
        async ({sha256, message, uploadId, path, size, tags, trainScene, trainTarget}: CompletePartUploadProps) => {
            try {
                await apiPostCompletePartUpload({
                    sha256,
                    message,
                    uploadId,
                    onlyUpload: true,
                });
                await commitPartUpload({message, files: [{sha256, uploadId, path, size}], tags, trainScene, trainTarget});
            }
            catch (error) {
                hideUploadLoading();
                setUploadProgress({key: path, progress: -1});
                console.error(error.message);
            }
        },
        [commitPartUpload, hideUploadLoading]
    );

    const initUploadBigFile = useCallback(
        async ({path, message, file, tags, trainScene, trainTarget, sha256}: UploadFileProps) => {
            if (file) {
                setShe256(sha256);
                setUploadProgress({key: path, progress: 0});
                const allPartNum = Math.ceil(file.size / PART_FILE_SIZE);
                try {
                    const uploadId = await apiPostInitPartUpload({
                        sha256,
                        repo,
                        branch,
                        path,
                        allPartNum,
                    });
                    setUploadId(uploadId);
                    setUploadProgress({key: path, progress: 2});

                    let uploadCancelled = false;

                    const handleUploadError = (error: any) => {
                        uploadCancelled = true;
                        hideUploadLoading();
                        setUploadProgress({key: path, progress: -1});
                        console.error(error?.message || '上传失败');
                        cancelPartUpload(sha256, uploadId);
                    };

                    await uploadBigFile({uploadId, file, sha256, path}, handleUploadError);

                    if (!uploadCancelled) {
                        completePartUpload({sha256, message, uploadId, size: file.size, path, tags, trainScene, trainTarget});
                    }
                }
                catch (error) {
                    hideUploadLoading();
                    setUploadProgress({key: path, progress: -1});
                    console.error(error.message);
                }
            }
        },
        [branch, cancelPartUpload, completePartUpload, hideUploadLoading, repo, uploadBigFile]
    );

    const uploadFunction = useCallback(
        async ({path, message, file, trainScene, trainTarget, url, tags, sha256}: UploadFileProps) => {
            if (file) {
                if (file.size < SMALL_FILE_SIZE) {
                    await uploadSmallFile({path, message, file, trainScene, trainTarget, tags, sha256});
                }
                else {
                    await initUploadBigFile({path, message, file, trainScene, trainTarget, tags, sha256});
                }
            }
            else {
                await uploadBosUrl({path, message, trainScene, trainTarget, url, tags});
            }
        },
        [initUploadBigFile, uploadBosUrl, uploadSmallFile]
    );

    useEffect(
        () => {
            const listener = (e: Event) => {
                e.preventDefault();
                e.returnValue = false;
            };
            if (loading) {
                window.addEventListener('beforeunload', listener);
            }
            else {
                window.removeEventListener('beforeunload', listener);
            }
            return () => {
                window.removeEventListener('beforeunload', listener);
            };
        },
        [loading]
    );

    return {
        onUpload: uploadFunction,
        onCancel: cancelPartUpload,
        refetchFileTree,
        loading,
        cancelLoading,
        calculateSHA256,
    };
};

export default useUpload;
