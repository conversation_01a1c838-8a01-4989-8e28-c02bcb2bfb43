import {useCallback, useState, useEffect} from 'react';
import {Modal, Space, Tooltip} from 'antd';
import {message, Button, Tag} from '@panda-design/components';
import {useFieldValue, useFormContext, useFormSubmit} from '@panda-design/path-form';
import styled from '@emotion/styled';
import {resetUploadProgress} from '@/regions/icode/dataset/fileUploadRegion';
import useUpload, {UploadFileProps} from './useUpload';
import UploadProgress from './UploadProgress';
import UploadComponent from './UploadComponent';

const FooterContainer = styled.div`
    display: flex;
    justify-content: space-between;
    width: 100%;
`;

export interface FileFormData {
    mode: 'bos' | 'local';
    message: string;
    trainScene: string;
    trainTarget: string;
    tags: string[];
    url: string;
    file: File;
    fileFolder: string;
    filePath: string;
    fileList: File[];
    sha256: string;
}

interface Props {
    open: boolean;
    onClose: () => void;
    onSuccess?: (file: UploadFileProps[]) => void;
}

const UploadDatasetFile = ({open, onClose, onSuccess}: Props) => {
    const {resetFields} = useFormContext();
    const mode = useFieldValue('mode');
    const fileList = useFieldValue('fileList');
    const urlList = useFieldValue('urlList');
    const isLocalMode = mode !== 'bos';
    const [isUploading, setIsUploading] = useState<boolean | null>(null);
    const {onUpload} = useUpload();

    useEffect(
        () => {
            if (open) {
                setIsUploading(false);
            }
        },
        [open]
    );

    const isCurrentListEmpty = useCallback(
        () => {
            const currentList = isLocalMode ? fileList : urlList;
            return !currentList || currentList.length === 0;
        },
        [isLocalMode, fileList, urlList]
    );

    const createUploadParams = useCallback(
        (file: FileFormData) => {
            const path = file.fileFolder === '根目录' || file.fileFolder === '/'
                ? file.filePath
                : `${file.fileFolder}/${file.filePath}`;

            const fileOrUrl = isLocalMode ? {file: file.file} : {url: file.url};

            return {
                path,
                message: file.message,
                trainScene: file.trainScene,
                trainTarget: file.trainTarget,
                tags: file.tags,
                sha256: file.sha256,
                ...fileOrUrl,
            };
        },
        [isLocalMode]
    );

    const uploadFiles = useCallback(
        async (params: UploadFileProps | UploadFileProps[]) => {
            const paramsArray = Array.isArray(params) ? params : [params];

            try {
                await Promise.all(
                    paramsArray.map(param =>
                        onUpload(param).catch(error => {
                            message.error(`文件 ${param.path} 上传失败`);
                            throw error;
                        })
                    )
                );
            }
            catch (error) {
                console.error('部分文件上传失败:', error);
            }
        },
        [onUpload]
    );

    const handleSubmit = useFormSubmit(
        async values => {
            const listToUse = isLocalMode ? values.fileList : values.urlList;
            setIsUploading(true);
            const uploadParams = listToUse.map(createUploadParams);
            await uploadFiles(uploadParams);
            if (uploadParams?.length && onSuccess) {
                onSuccess(uploadParams);
            }
        }
    );

    const handleRetry = useCallback(
        (path: string) => {
            const listToUse = isLocalMode ? fileList : urlList;
            const fileToRetry = listToUse?.find((file: FileFormData) => {
                const filePath = file.fileFolder === '根目录' || file.fileFolder === '/'
                    ? file.filePath
                    : `${file.fileFolder}/${file.filePath}`;
                return filePath === path;
            });

            if (fileToRetry) {
                const params = createUploadParams(fileToRetry);
                uploadFiles(params);
            }
        },
        [isLocalMode, fileList, urlList, createUploadParams, uploadFiles]
    );

    const handleOnCancel = useCallback(
        () => {
            onClose();
            resetFields();
            resetUploadProgress();
        },
        [onClose, resetFields]
    );

    const handleAllCompleted = useCallback(
        () => {
            handleOnCancel();
        },
        [handleOnCancel]
    );

    return (
        <Modal
            title="上传文件"
            open={open}
            onCancel={handleOnCancel}
            width={isUploading ? 600 : 1000}
            maskClosable={false}
            destroyOnClose
            footer={
                !isUploading && (
                    <FooterContainer>
                        <span>已经添加文件&nbsp;
                            <Tag type="flat" color="info">
                                {
                                    (isLocalMode ? fileList?.length : urlList?.length) || 0
                                }
                            </Tag>个
                        </span>
                        <Space>
                            <Button onClick={handleOnCancel}>取消</Button>
                            <Tooltip title={isCurrentListEmpty() && '未添加文件'}>
                                <Button
                                    type="primary"
                                    onClick={handleSubmit}
                                    disabled={isCurrentListEmpty()}
                                >
                                    上传
                                </Button>
                            </Tooltip>
                        </Space>
                    </FooterContainer>
                )}
        >
            {isUploading ? (
                <UploadProgress
                    onRetry={handleRetry}
                    onAllCompleted={handleAllCompleted}
                />
            ) : (
                <UploadComponent />
            )}
        </Modal>
    );
};

export default UploadDatasetFile;
