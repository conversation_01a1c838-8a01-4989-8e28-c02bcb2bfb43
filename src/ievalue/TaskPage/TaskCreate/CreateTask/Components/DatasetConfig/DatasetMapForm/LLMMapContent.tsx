/* eslint-disable max-lines */
import {Form, Select, Typography} from 'antd';
import {useEffect, useMemo, useState} from 'react';
import {apiGetModelParams} from '@/api/ievalue/model';
import {AddMethodTypeEnum, TargetTypeEnum} from '@/constants/ievalue/model';
import {useIsYiYanSpace} from '@/hooks/ievalue/spacePage';
import {TemplateStageTypesEnum} from '@/constants/ievalue/task';
import {
    useCreateTaskIsQuickEvaluate,
    useCreateTaskSpaceCode,
    useIsMutilModal,
} from '../../CreateTaskProvider/CreateTaskBaseProvider';
import {useCreateTaskTemplateStageTypes} from '../../CreateTaskProvider/DatasetProvider';
import {TargetMapFormContentProps, DatasetOptionsProps} from './DatasetMapFormContent';
import {MultiModalFormItem} from './MultiModalFormItem';

type Props = TargetMapFormContentProps & DatasetOptionsProps;
const LLMMapContent = ({
    includesMultiModal,
    modelSelects,
    datasetOptions,
}: Props) => {
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const [modelParamsMap, setModelParamsMap] = useState<any>({});
    const stageTypes = useCreateTaskTemplateStageTypes();
    const spaceCode = useCreateTaskSpaceCode();
    const isYiYangSpace = useIsYiYanSpace(spaceCode);

    const required = useMemo(
        () => {
        // 一言自动化项目，并且不只有推理阶段时
            return stageTypes.join(',') !== TemplateStageTypesEnum.NEW_PREDICTING && isYiYangSpace;
        },
        [isYiYangSpace, stageTypes]
    );

    // 获取自定义模型得模型参数
    useEffect(
        () => {
            if (modelSelects?.length > 0) {
                for (const i of modelSelects) {
                    if (i.config.addMethod === AddMethodTypeEnum.CUSTOM) {
                        apiGetModelParams({
                            ID: i.config.modelID,
                            isEvaluate: true,
                            isShow: false,
                            isQuery: false,
                        })
                            .then(data => {
                                setModelParamsMap((param: any) => {
                                    return {...param, [i.config.modelID]: data};
                                });
                            })
                            .catch(() => {});
                    }
                }
            }
        },
        [modelSelects]
    );
    const isMutilModal = useIsMutilModal();
    return (
        <div>
            <Form.Item
                label="Query"
                name="input"
                rules={[{required: true, message: '未匹配到，请手动选择'}]}
            >
                <Select allowClear options={datasetOptions} />
            </Form.Item>
            {required && (
                <>
                    <Form.Item
                        label="Query分类"
                        name="querySort"
                        rules={[
                            {
                                required: true,
                                message: '请选择Query分类',
                            },
                        ]}
                    >
                        <Select
                            mode="multiple"
                            allowClear
                            options={datasetOptions}
                        />
                    </Form.Item>
                    <Form.Item
                        label="参考答案"
                        name="referenceOutput"
                        tooltip="ground_truth"
                        rules={[
                            {
                                required: true,
                                message: '请选择参考答案',
                            },
                        ]}
                    >
                        <Select allowClear options={datasetOptions} />
                    </Form.Item>
                    <Form.Item
                        label="参考文章"
                        name="reference"
                        rules={[
                            {
                                required: true,
                                message: '请选择参考文章',
                            },
                        ]}
                    >
                        <Select allowClear options={datasetOptions} />
                    </Form.Item>
                </>
            )}
            {(isQuickEvaluate || !modelSelects?.length) && (
                <Form.Item
                    label="推理结果"
                    name="outputList"
                    style={{margin: 0}}
                    rules={[
                        {
                            required: true,
                            message: '请选择推理结果',
                        },
                    ]}
                >
                    <Select
                        mode="multiple"
                        allowClear
                        options={datasetOptions}
                    />
                </Form.Item>
            )}
            {includesMultiModal && (
                <>
                    <MultiModalFormItem datasetOptions={datasetOptions} required />
                    <Form.Item label="参考音频URL" name="referenceAudioLink">
                        <Select allowClear options={datasetOptions} />
                    </Form.Item>
                </>
            )}
            {modelSelects?.length > 0 && !isMutilModal && (
                <>
                    <Form.Item
                        label="推理结果"
                        style={{margin: 0}}
                        tooltip={
                            stageTypes?.includes('PREDICTING')
                                ? '本流程包含推理阶段，配置列映射的模型将跳过推理阶段，直接使用评估集对应列映射的内容做为推理结果。'
                                : undefined
                        }
                    >
                        {modelSelects?.map(item => (
                            <Form.Item
                                label={
                                    <Typography.Text
                                        style={{maxWidth: 126}}
                                        ellipsis={{
                                            tooltip: item.config.modelName,
                                        }}
                                    >
                                        {item.config.modelName}
                                    </Typography.Text>
                                }
                                name={['output', `${item.config.modelID}`]}
                                key={item.config.modelID}
                                className="modal-item"
                                rules={[
                                    {
                                        required:
                                            !stageTypes?.includes('PREDICTING'),
                                        message: '未匹配到，请手动选择',
                                    },
                                ]}
                            >
                                <Select
                                    allowClear
                                    options={datasetOptions}
                                />
                            </Form.Item>
                        ))}
                    </Form.Item>
                    {modelSelects?.map(item => (
                        <div key={item.config.modelID}>
                            {item.config.addMethod === 2
                                && item.config.target === TargetTypeEnum.AI && (
                                <Form.Item
                                    label={
                                        <Typography.Text
                                            style={{width: 126}}
                                            ellipsis={{
                                                tooltip:
                                                        item.config.modelName,
                                            }}
                                        >
                                            {item.config.modelName}
                                        </Typography.Text>
                                    }
                                >
                                    {modelParamsMap[item.config.modelID]
                                        ?.length > 0 && (
                                        <>
                                            {modelParamsMap[
                                                item.config.modelID
                                            ]?.map(
                                                (
                                                    param: any,
                                                    index: number
                                                ) => (
                                                    <Form.Item
                                                        label={
                                                            <Typography.Text
                                                                style={{
                                                                    maxWidth: 126,
                                                                }}
                                                                ellipsis={{
                                                                    tooltip:
                                                                            param.name,
                                                                }}
                                                            >
                                                                {param.name}
                                                            </Typography.Text>
                                                        }
                                                        name={[
                                                            'customAIParam',
                                                            `${item.config.modelID}`,
                                                            index,
                                                            `${param.id}`,
                                                        ]}
                                                        rules={[
                                                            {
                                                                required:
                                                                        true,
                                                                message:
                                                                        '未匹配到，请手动选择',
                                                            },
                                                        ]}
                                                        key={`customAIParam-${item.config.modelID}-${param.id}`}
                                                        className="modal-item"
                                                    >
                                                        <Select
                                                            allowClear
                                                            options={
                                                                datasetOptions
                                                            }
                                                        />
                                                    </Form.Item>
                                                )
                                            )}
                                        </>
                                    )}
                                </Form.Item>
                            )}
                        </div>
                    ))}
                </>
            )}
        </div>
    );
};

export default LLMMapContent;
