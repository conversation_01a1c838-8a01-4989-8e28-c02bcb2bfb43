/* eslint-disable complexity */
import {Alert, Form} from 'antd';
import {Button} from '@panda-design/components';
import {useSwitch} from 'huse';
import {isEmpty} from 'lodash';
import {useCallback, useEffect, useMemo} from 'react';
import {apiWsDatasetAnalyzeV1, apiCreateDatasetMap} from '@/api/ievalue/task';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    loadDatasetMapInfo,
    useDatasetMapInfo,
} from '@/regions/ievalue/task/createTaskForm';
import {TargetEnum} from '@/constants/ievalue/task';
import {
    useCreateTaskIsQuickEvaluate,
    useCreateTaskSpaceCode,
    useCreateTaskTarget,
    useCreateTaskTemplateID,
} from '../../CreateTaskProvider/CreateTaskBaseProvider';
import {
    DatasetMapFormContentProps,
    DatasetMapFormContent,
} from './DatasetMapFormContent';

interface DatasetMapFormProps extends DatasetMapFormContentProps {
    onCancel?: () => void;
    onFinish?: (datasetID: number, mapTemplateID: number) => void;
}

export const DatasetMapForm = ({
    onCancel,
    modelSelects,
    promptSelects,
    datasetID,
    onFinish,
}: DatasetMapFormProps) => {
    const isQuickEvaluate = useCreateTaskIsQuickEvaluate();
    const target = useCreateTaskTarget();

    const spaceCode = useCreateTaskSpaceCode();
    const templateID = useCreateTaskTemplateID();
    const mapTemplateInfo = useDatasetMapInfo(datasetID);
    const [form] = Form.useForm();
    const [loading, startLoading, stopLoading] = useSwitch(false);

    useEffect(
        () => {
            loadDatasetMapInfo({datasetID, templateID});
        },
        [datasetID, templateID]
    );

    useEffect(
        () => {
            if (mapTemplateInfo) {
                form.resetFields();
            }
        },
        [form, mapTemplateInfo]
    );

    const promptVersionIDs = useMemo(
        () => {
            if (promptSelects) {
                if (Array.isArray(promptSelects)) {
                    return promptSelects?.map((e: any) => {
                        if (Array.isArray(e) && e.length > 1) {
                            return e[1];
                        }
                        return 0;
                    });
                }
                return [promptSelects];
            }
            return undefined;
        },
        [promptSelects]
    );

    const onSubmit = useCallback(
        async (formData: any) => {
            startLoading();
            const taskType =
                (target === TargetEnum.LLM && !modelSelects?.length)
                    || isQuickEvaluate
                    ? 'fastEvaluation'
                    : '';
            const allModelID = modelSelects?.map((item: {config: any}) => {
                return item.config.modelID;
            });
            const payload = {
                spaceCode,
                ...formData,
                querySort: formData?.querySort?.join(',') ?? '',
                multiModal: formData?.multiModal?.join(',') ?? '',
                otherInfo: formData?.otherInfo?.join(',') ?? '',
                stageOperator: formData?.stageOperator ?? {},
                output: formData?.output ?? {},
                taskType,
                customApiParam: {
                    ...(formData?.customAIParam ?? {}),
                    ...(formData?.customLLMParam ?? {}),
                },
                referenceIndicator:
                    formData?.referenceIndicator?.join(',') ?? '',
                allModelID,
                templateID,
            };
            try {
                const responseData = await apiCreateDatasetMap(payload);
                const mapTemplateID = responseData.ID;
                await apiWsDatasetAnalyzeV1({
                    mapTemplateID,
                    datasetID,
                    templateID,
                    promptVersionIDs,
                    modelID: allModelID,
                    taskType,
                });
                onFinish?.(datasetID, mapTemplateID);
                loadDatasetMapInfo({datasetID, templateID});
            } catch (e: any) {
                /* empty */
            } finally {
                stopLoading();
            }
        },
        [
            datasetID,
            isQuickEvaluate,
            modelSelects,
            onFinish,
            promptVersionIDs,
            spaceCode,
            startLoading,
            stopLoading,
            target,
            templateID,
        ]
    );

    return (
        <>
            {!isEmpty(mapTemplateInfo) && (
                <Alert
                    type="warning"
                    message={<span>已经映射过，重新提交会覆盖</span>}
                    showIcon
                    banner
                    style={{margin: '0 0 10px 0'}}
                />
            )}
            <Form
                form={form}
                initialValues={{
                    ...mapTemplateInfo,
                    querySort: mapTemplateInfo?.querySort
                        ? mapTemplateInfo?.querySort?.split(',')
                        : [],
                    multiModal: mapTemplateInfo?.multiModal
                        ? mapTemplateInfo?.multiModal?.split(',')
                        : [],
                    otherInfo: mapTemplateInfo?.otherInfo
                        ? mapTemplateInfo?.otherInfo?.split(',')
                        : [],
                    customAIParam: mapTemplateInfo?.customApiParam ?? {},
                    customLLMParam: mapTemplateInfo?.customApiParam ?? {},
                    referenceIndicator: mapTemplateInfo?.referenceIndicator
                        ? mapTemplateInfo?.referenceIndicator?.split(',')
                        : [],
                }}
                onFinish={onSubmit}
                labelCol={{flex: '150px'}}
                labelAlign="left"
                autoComplete="off"
                labelWrap
            >
                <CustomBoundary.Loading>
                    <DatasetMapFormContent
                        modelSelects={modelSelects}
                        promptSelects={promptSelects}
                        datasetID={datasetID}
                    />
                </CustomBoundary.Loading>
                <FlexLayout justify="center" gap={16} style={{marginTop: 20}}>
                    {onCancel && <Button onClick={onCancel}>上一步</Button>}
                    <Button type="primary" htmlType="submit" loading={loading}>
                        提交
                    </Button>
                </FlexLayout>
            </Form>
        </>
    );
};
