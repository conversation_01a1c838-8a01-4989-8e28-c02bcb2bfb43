import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {Field, FieldProps, useFormContext} from '@panda-design/path-form';
import {DatePicker, Select, Tabs, TreeSelect, Checkbox, Input} from 'antd';
import {useCallback, useMemo} from 'react';
import {useDirTreeContext} from '@/components/DirTree/DirTreeProvider';
import {UserSelect} from '@/components/ievalue/UserSelect';
import {colors} from '@/constants/colors';
import {TaskStagFilterOptions, TaskStatusOptions, UserUnAssignedObj} from '@/constants/ievalue/task';

const FormItem = (props: FieldProps) => (
    <Field colon hasGap={false} width={80} {...props} />
);

const Container = styled.div`
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    background-color: ${colors['gray-2']};
    padding: 24px;
    margin-top: 20px;

    .ant-field {
        margin-right: 0;
    }

    .dir-filter-row {
        grid-column: 1 / -1;
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 24px;
    }
`;

const TimeFilterRow = styled.div`
    grid-column: 1 / -1;
    display: flex;
    gap: 24px;
    align-items: flex-start;

`;

const ButtonGroup = styled.div`
    display: flex;
    gap: 16px;
`;

const FormItemWrapper = styled.div`
    width: 100%;
    .ant-select, .ant-picker {
        width: 100% !important;
    }
`;

interface TaskStatisticsProps {
    onSearch?: () => void;
}

const TaskStatistics = ({onSearch}: TaskStatisticsProps) => {
    const {setFieldValue} = useFormContext();
    const {dirDatas} = useDirTreeContext();
    const handleDirChange = useCallback(
        (values: number[]) => {
            setFieldValue('dirIDs', values);
        },
        [setFieldValue]
    );

    const renderFormItems = useMemo(
        () => (
            <>
                <div className="dir-filter-row">
                    <FormItemWrapper>
                        <FormItem
                            name="dirIDs"
                            label="任务目录"
                        >
                            <TreeSelect
                                showSearch
                                treeData={dirDatas}
                                placeholder="请选择任务目录"
                                onChange={handleDirChange}
                                treeDefaultExpandAll
                                virtual={false}
                                multiple
                                allowClear
                                dropdownStyle={{
                                    maxHeight: 300,
                                    overflow: 'auto',
                                    padding: '8px 0',
                                    overscrollBehavior: 'contain',
                                }}
                                fieldNames={{
                                    label: 'name',
                                    value: 'id',
                                    children: 'children',
                                }}
                            />
                        </FormItem>
                    </FormItemWrapper>
                    <FormItemWrapper>
                        <FormItem name="withSubDirs" type="checkbox" style={{marginLeft: 24}}>
                            <Checkbox>包含子目录</Checkbox>
                        </FormItem>
                    </FormItemWrapper>
                </div>
                <FormItemWrapper>
                    <FormItem name="name" label="任务名称">
                        <Input placeholder="请输入任务名称" allowClear />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper>
                    <FormItem name="stageStatus" label="任务状态">
                        <Select
                            allowClear
                            placeholder="任务状态"
                            options={TaskStatusOptions}
                        />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper>
                    <FormItem name="stageList" label="任务阶段">
                        <Select
                            allowClear
                            mode="multiple"
                            placeholder="任务阶段"
                            options={TaskStagFilterOptions}
                        />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper>
                    <FormItem name="reviewers" label="评估人员">
                        <UserSelect
                            defaultOptions={[{username: UserUnAssignedObj.label}]}
                            mode="multiple"
                            labelInValue={false}
                        />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper>
                    <FormItem name="auditors" label="质检人员">
                        <UserSelect
                            defaultOptions={[{username: UserUnAssignedObj.label}]}
                            mode="multiple"
                            labelInValue={false}
                        />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper>
                    <FormItem name="evaluateTime" label="评估时间">
                        <DatePicker.RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper>
                    <FormItem name="reviewTime" label="审核时间">
                        <DatePicker.RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
                    </FormItem>
                </FormItemWrapper>
                <FormItemWrapper className="time-filter">
                    <FormItem name="createTime" label="创建时间">
                        <DatePicker.RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
                    </FormItem>
                </FormItemWrapper>
            </>
        ),
        [handleDirChange, dirDatas]
    );

    return (
        <Container>
            {renderFormItems}
            <TimeFilterRow>
                <ButtonGroup>
                    <Button type="primary" onClick={onSearch}>
                        查询
                    </Button>
                </ButtonGroup>
            </TimeFilterRow>
        </Container>
    );
};

interface Props {
    onSearch: () => void;
    onTabChange: (isEfficiency: boolean) => void;
}
const StyledTabs = styled(Tabs)`
    .ant-tabs-content {
        background-color: ${colors['gray-2']};
        margin-top: 16px;
    }
`;

const DrawerFilterContent = ({onSearch, onTabChange}: Props) => {
    const handleTabChange = useCallback(
        (key: string) => {
            onTabChange(key === 'efficiency');
        },
        [onTabChange]
    );

    const items = useMemo(
        () => [
            {
                key: 'task',
                label: '任务统计',
            },
            {
                key: 'efficiency',
                label: '人效统计',
            },
        ],
        []
    );

    return (
        <div>
            <StyledTabs
                defaultActiveKey="task"
                items={items}
                onChange={handleTabChange}
            />
            <TaskStatistics onSearch={onSearch} />
        </div>
    );
};

export default DrawerFilterContent;
