/* eslint-disable max-lines */
import {FormProvider, useFormSubmit} from '@panda-design/path-form';
import {Flex, Modal, Table} from 'antd';
import {Button} from '@panda-design/components';
import dayjs from 'dayjs';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {RollbackOutlined} from '@ant-design/icons';
import QRCodeSrc from '@/assets/img/serviceNumberQRcode.png';
import {
    apiTaskGetDirectoryEvaluationStatsWs,
    apiTaskGetHumanEfficiencyStatsWs,
    TaskHumanEfficiencyData,
    TaskStatisticalData,
    TaskGetDirectoryEvaluationStatsParams,
    apiTaskGetHumanEfficiencyDetail,
    HumanEfficiencyDetailData,
    apiTaskGetTaskDetail,
    taskDetailData,
    apiDirTaskInfoTempFileGenerate,
} from '@/api/ievalue/task';
import DirTreeProvider from '@/components/DirTree/DirTreeProvider';
import {useSpacePartnerIsMEG} from '@/hooks/ievalue/settings';
import {useSpaceCode} from '@/hooks/ievalue/spacePage';
import {commonSubmitFail} from '@/utils/form';
import {SpaceUrl} from '@/links/ievalue/space';
import TaskListProvider, {useTaskListContext} from '../TaskListProvider';
import {getColumns} from '../columns/index';
import DrawerFilterContent from './DrawerFilterContent';
import DetailTable from './DetailTable';
export type TableData = TaskStatisticalData | TaskHumanEfficiencyData;

const formatParams = (
    formValues: any
): TaskGetDirectoryEvaluationStatsParams => {
    const dirIDs = formValues.dirIDs || [];
    const params: TaskGetDirectoryEvaluationStatsParams = {
        dirIDs,
        withSubDirs: !!formValues.withSubDirs,
    };
    params.name = formValues.name;
    params.stageStatus = formValues.stageStatus;
    params.stageList = formValues.stageList;
    params.reviewers = formValues.reviewers;
    params.auditors = formValues.auditors;
    if (formValues.evaluateTime?.[0] && formValues.evaluateTime?.[1]) {
        params.evaluateTimeStart = dayjs(formValues.evaluateTime[0]).format(
            'YYYY-MM-DD HH:mm:ss'
        );
        params.evaluateTimeEnd = dayjs(formValues.evaluateTime[1]).format(
            'YYYY-MM-DD HH:mm:ss'
        );
    }
    if (formValues.reviewTime?.[0] && formValues.reviewTime?.[1]) {
        params.reviewTimeStart = dayjs(formValues.reviewTime[0]).format(
            'YYYY-MM-DD HH:mm:ss'
        );
        params.reviewTimeEnd = dayjs(formValues.reviewTime[1]).format(
            'YYYY-MM-DD HH:mm:ss'
        );
    }
    if (formValues.createTime?.[0] && formValues.createTime?.[1]) {
        params.createTimeStart = dayjs(formValues.createTime[0]).format(
            'YYYY-MM-DD HH:mm:ss'
        );
        params.createTimeEnd = dayjs(formValues.createTime[1]).format(
            'YYYY-MM-DD HH:mm:ss'
        );
    }
    return params;
};

const getTaskDetailData = async (
    rowIndex: number,
    spaceCode: string,
    taskData: TableData[],
    drillDownType: string,
    formValues: any
) => {
    const {dirIDs, withSubDirs, ...restParams} = formValues;
    const params = {
        dirID: taskData[rowIndex].dirID,
        spaceCode,
        drillDownType,
        ...restParams,
    };
    const result = await apiTaskGetTaskDetail(params);
    return {params, result};
};

const getEfficiencyDrillDownData = async (
    rowIndex: number,
    drillDownType: string,
    taskData: TableData[],
    formValues: any,
    spaceCode: string
) => {
    const newReviewers = (drillDownType === 'evaluateAccuracy' || drillDownType === 'evaluateTime')
        ? [taskData[rowIndex].userName]
        : formValues.reviewers;
    const newAuditors = (drillDownType === 'reviewTime') ? [taskData[rowIndex].userName] : formValues.auditors;
    const {dirIDs, withSubDirs, auditors, reviewers, ...restParams} = formValues;

    return {
        result: await apiTaskGetHumanEfficiencyDetail({
            dirID: taskData[rowIndex].dirID,
            spaceCode,
            drillDownType: drillDownType,
            reviewers: newReviewers,
            auditors: newAuditors,
            ...restParams,
        }),
    };
};

const StatisticalContent = () => {
    const navigate = useNavigate();
    const isMEGSpace = useSpacePartnerIsMEG();
    const [isEfficiencyTab, setIsEfficiencyTab] = useState(false);
    const spaceCode = useSpaceCode();
    const [taskData, setTaskData] = useState<TaskStatisticalData[] | null>(
        null
    );
    const [pending, setPending] = useState(false);
    const [detailTableType, setDetailTableType] = useState(null);
    const [detailData, setDetailData] = useState<HumanEfficiencyDetailData | taskDetailData | null>(null);
    const [detailDataLoading, setDetailDataLoading] = useState(false);
    const [formValues, setFormValues] = useState<any | null>(null);
    const exportDataRef = useRef(null);

    useEffect(
        () => {
            setTaskData([]);
            setDetailTableType(null);
        },
        [isEfficiencyTab]
    );

    const handleSearch = useFormSubmit(async (values: any) => {
        setPending(true);
        setDetailTableType(null);
        const formatValues = formatParams(values);
        const params = {spaceCode, ...formatValues};
        setFormValues(formatValues);
        try {
            if (isEfficiencyTab) {
                const result = await apiTaskGetHumanEfficiencyStatsWs(params);
                setTaskData(result ?? []);
            } else {
                const result = await apiTaskGetDirectoryEvaluationStatsWs(
                    params
                );
                setTaskData(result ?? []);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            setTaskData([]);
        } finally {
            setPending(false);
        }
    }, commonSubmitFail);

    const handleBack = useCallback(
        () => {
            navigate(SpaceUrl.task.toUrl({spaceCode}));
        },
        [navigate, spaceCode]
    );

    const handleCellClick = useCallback(
        async (rowIndex: number, drillDownType: string) => {
            try {
                setDetailDataLoading(true);
                setDetailTableType(drillDownType);
                if (isEfficiencyTab) {
                    const {result} = await getEfficiencyDrillDownData(
                        rowIndex,
                        drillDownType,
                        taskData,
                        formValues,
                        spaceCode
                    );
                    setDetailData(result ?? null);
                } else {
                    const {params, result} = await getTaskDetailData(
                        rowIndex,
                        spaceCode,
                        taskData,
                        drillDownType,
                        formValues
                    );
                    setDetailData(result ?? null);
                    exportDataRef.current = params;
                }
            } catch (error) {
                console.error('获取数据失败:', error);
                setDetailData(null);
            } finally {
                setDetailDataLoading(false);
            }
        },
        [isEfficiencyTab, taskData, formValues, spaceCode]
    );

    const isRowClickable = useCallback(
        (record: TableData, type: string) => {
            if (type === 'evaluateAccuracy' && !record?.evaluateAccuracy) {
                return false;
            }
            if (type === 'zeroScoreSession' && record?.zeroSessionCount === 0) {
                return false;
            }
            if (type === 'notPass' && record?.rejectedAgentCount === 0) {
                return false;
            }
            if (record?.userName?.includes('+')) {
                return false;
            }
            if (record?.dirName?.includes('+')) {
                return false;
            }
            return true;
        },
        []
    );

    const handleExportClick = useCallback(
        async () => {
            try {
                await apiDirTaskInfoTempFileGenerate(exportDataRef.current);
                Modal.success({
                    title: '提交成功',
                    width: '600px',
                    content: (
                        <Flex vertical align="center" gap={20}>
                            <span>
                                请在如流关注Comate
                                Stack服务号，导出数据完成后将会通过服务号通知您。
                            </span>
                            <img style={{width: 170}} src={QRCodeSrc} />
                        </Flex>
                    ),
                });
            } catch (error) {
                console.error('导出失败:', error);
            }
        },
        []
    );

    return (
        <>
            <Button onClick={handleBack} icon={<RollbackOutlined />}>
                返回上一级
            </Button>
            <DrawerFilterContent
                onSearch={handleSearch}
                onTabChange={setIsEfficiencyTab}
            />
            {detailTableType ? (
                <Flex vertical gap={8} style={{marginTop: '20px'}}>
                    <Flex justify="space-between" align="center">
                        <Button
                            onClick={() => {
                                setDetailTableType(null);
                                setDetailData(null);
                                exportDataRef.current = null;
                            }}
                            icon={<RollbackOutlined />}
                        >
                            返回
                        </Button>
                        {!!exportDataRef.current && (
                            <Button onClick={handleExportClick}>
                                导出excel数据
                            </Button>
                        )}
                    </Flex>
                    <DetailTable
                        dataSource={detailData}
                        loading={detailDataLoading}
                        detailTableType={detailTableType}
                        isEfficiencyTab={isEfficiencyTab}
                    />
                </Flex>
            ) : (
                <Table<TableData>
                    bordered
                    size="middle"
                    style={{marginTop: '20px'}}
                    columns={getColumns(
                        isMEGSpace,
                        isEfficiencyTab,
                        handleCellClick,
                        isRowClickable
                    )}
                    loading={pending}
                    dataSource={taskData}
                    scroll={{x: 'max-content'}}
                    pagination={{
                        pageSize: 100,
                        showSizeChanger: false,
                    }}
                    rowKey="index"
                />
            )}
        </>
    );
};

const FormStatisticsContent = () => {
    const location = useLocation();
    const searchParams = new URLSearchParams(location?.search);
    const stage = searchParams.get('stage');
    const dirID = Number(searchParams.get('dirID'));

    const initialValues = useMemo(
        () => ({
            dirIDs: dirID ? [dirID] : [],
            stageList: stage ? [stage] : [],
        }),
        [dirID, stage]
    );

    return (
        <FormProvider initialValues={initialValues}>
            <StatisticalContent />
        </FormProvider>
    );
};

const StatisticsWithDirTree = () => {
    const {listRef, dirDatas, refreshDirList, onDirChange} =
        useTaskListContext();

    return (
        <DirTreeProvider
            dirDatas={dirDatas}
            refreshDirList={refreshDirList}
            onDirChange={onDirChange}
            refresh={listRef?.current?.refresh}
            expandFirstLevel
        >
            <FormStatisticsContent />
        </DirTreeProvider>
    );
};

const StatisticsPage = () => {
    return (
        <TaskListProvider>
            <StatisticsWithDirTree />
        </TaskListProvider>
    );
};

export default StatisticsPage;
