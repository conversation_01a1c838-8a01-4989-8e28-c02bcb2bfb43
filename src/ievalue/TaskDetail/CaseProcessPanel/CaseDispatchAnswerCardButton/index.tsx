import {Button} from '@panda-design/components';
import {Pagination, Popover, Select, Spin, Typography} from 'antd';
import {useEffect, useState} from 'react';
import {useLocalStorage} from 'huse';
import {CustomBoundary} from '@/components/ievalue/Boundary';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {
    useTaskCaseListPagination,
    useTaskStageID,
} from '@/hooks/ievalue/task';
import {useCurrentUser} from '@/regions/user/currentUser';
import {DimensionEnum} from '@/api/ievalue/tags';
import {TaskStatusEnum} from '@/constants/ievalue/task';
import {CaseButton} from '../AnswerCardButton';

enum CaseSelectValueEnum {
    ALL = 'ALL',
    NOTPASS = 'NOTPASS',
    RUNNING = 'RUNNING',
    SPECIAL = 'SPECIAL',
    LIKE = 'LIKE',
    NOTLIKE = 'NOTLIKE',
    SAMPLING = 'SAMPLING',
}

const CaseSelectOption = [
    {value: CaseSelectValueEnum.ALL, label: '全部'},
    {value: CaseSelectValueEnum.NOTPASS, label: '只看审核不通过'},
    {value: CaseSelectValueEnum.RUNNING, label: '只看未答'},
    {value: CaseSelectValueEnum.SPECIAL, label: '只看待讨论'},
    {value: CaseSelectValueEnum.LIKE, label: '只看点赞'},
    {value: CaseSelectValueEnum.NOTLIKE, label: '只看点踩'},
    {value: CaseSelectValueEnum.SAMPLING, label: '只看未抽样'},
];

const CaseSelectSearchValueMap = {
    [CaseSelectValueEnum.ALL]: {},
    [CaseSelectValueEnum.NOTPASS]: {pass: 1},
    [CaseSelectValueEnum.RUNNING]: {stageStatus: TaskStatusEnum.RUNNING},
    [CaseSelectValueEnum.SPECIAL]: {feedbacks: [DimensionEnum.SPECIAL]},
    [CaseSelectValueEnum.LIKE]: {feedbacks: [DimensionEnum.LIKE]},
    [CaseSelectValueEnum.NOTLIKE]: {feedbacks: [DimensionEnum.NOTLIKE]},
    [CaseSelectValueEnum.SAMPLING]: {sampled: 0},
};

function useLocalStorageAnswerCardSelect() {
    return useLocalStorage<CaseSelectValueEnum>(
        'IEVALUE/Task/Evaluate/CaseDispatchAnswerCard/Select',
        CaseSelectValueEnum.ALL
    );
}

const AnswerCardContent = ({status}: { status: CaseSelectValueEnum }) => {
    const stageID = useTaskStageID();
    const userInfo = useCurrentUser();
    const {dataSource, setSearchObj, pagination, pending} =
        useTaskCaseListPagination({
            ...CaseSelectSearchValueMap[status],
            stageID,
            username: userInfo?.username,
        });

    useEffect(
        () => {
            setSearchObj({
                ...CaseSelectSearchValueMap[status],
                stageID,
                username: userInfo?.username,
            });
        },
        [setSearchObj, stageID, status, userInfo?.username]
    );
    return (
        <Spin spinning={pending}>
            <FlexLayout
                wrap="wrap"
                gap={8}
                style={{width: '432px', height: '160px', overflowY: 'auto'}}
            >
                {dataSource.map(item => (
                    <CaseButton
                        key={item.caseID}
                        caseItem={item}
                        status={item.status}
                    />
                ))}
            </FlexLayout>
            <Pagination {...pagination} size="small" showQuickJumper />
        </Spin>
    );
};

const CaseDispatchAnswerCardButton = () => {
    const [open, setOpen] = useState(false);
    const [status, setStatus] = useLocalStorageAnswerCardSelect();

    return (
        <Popover
            destroyTooltipOnHide
            content={
                <CustomBoundary.Loading>
                    <AnswerCardContent status={status} />
                </CustomBoundary.Loading>
            }
            title={
                <FlexLayout id="answer-panel" justify="space-between">
                    <Typography.Text strong>答题卡</Typography.Text>
                    <Select<CaseSelectValueEnum>
                        style={{width: 150}}
                        bordered={false}
                        value={status}
                        options={CaseSelectOption}
                        onChange={setStatus}
                        getPopupContainer={() =>
                            document.getElementById('answer-panel')
                        }
                    />
                </FlexLayout>
            }
            trigger="click"
            placement="topLeft"
            open={open}
            onOpenChange={setOpen}
        >
            <Button type="text">答题卡</Button>
        </Popover>
    );
};

export default CaseDispatchAnswerCardButton;
