/* eslint-disable max-lines */
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {Col, Flex, Input, Row, Space, Splitter} from 'antd';
import {debounce, head} from 'lodash';
import {useCallback, useEffect, useMemo, useState, ReactNode} from 'react';
import {
    apiCaseEvaluateUpsert,
    apiEvaluateCaseUpsert,
    apiEvaluateRecordUpdate,
    CaseEvaluateRecordItem,
    GroupCaseInfoItem,
    ScoreItem,
} from '@/api/ievalue/case';
import backgroundImage from '@/assets/ievalue/img/dl_card_bj.png';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {CaseHistoryTypeEnum, CaseStatusEnum} from '@/constants/ievalue/case';
import {GSB_MANUAL_POLICY_ID} from '@/constants/ievalue/evaluate';
import {TaskStageEnum} from '@/constants/ievalue/task';
import {useSpaceCodeSafe} from '@/hooks/ievalue/spacePage';
import {
    useCaseEvaluateList,
    useCaseStageType,
    useGroupStageInfo,
    useStrategyTaskList,
    useTaskInfo,
    useTaskTaskID,
} from '@/hooks/ievalue/task';
import {LayoutOptionEnum} from '@/constants/ievalue/taskDetail';
import {FullScreenComponent} from '@/components/TaskGroup/FullScreen';
import NotePanel from '@/components/TaskGroup/NotePanelComponent/NotePanel';
import {setEvaluateOnceSplitterWidth, useEvaluateOnceSplitterWidth} from '@/regions/evaluate/onceSplitterWidth';
import {AutoEvaluateScorePanel} from '../../TaskGroupDetail/components/AutoEvaluateScorePanel';
import {ScoreHistoryButton} from '../../TaskGroupDetail/components/ScoreHistoryButton';
import Back2BackScoreButton from '../../TaskGroupDetail/components/Back2BackScoreButton';
import FeedbackTagPanel from '../../TaskGroupDetail/components/TagPanelComponent/FeedbackTagPanel';
import AddTagPanel from '../../TaskGroupDetail/components/TagPanelComponent/AddTagPanel';
import AutoComputePanel from '../../TaskGroupDetail/components/AutoComputePanelComponent/AutoComputePanel';
import {DiffAlertTitle} from './DiffAlertTitle';
import {ScoreGroup} from './ScoreGroup';

const ScorePanel = styled(FlexLayout)`
    border-left: 1px solid #e1e1e1;
`;

interface ModelResultProps {
    recordItem: CaseEvaluateRecordItem;
    title: ReactNode;
    onRefresh?: () => void;
    groupCaseInfo: GroupCaseInfoItem;
    remarkVisible: boolean;
    layout: LayoutOptionEnum;
}

export const ModelResult = ({
    recordItem,
    title,
    onRefresh,
    groupCaseInfo,
    remarkVisible,
    layout,
}: ModelResultProps) => {
    const [taskInfo] = useTaskInfo();
    const spaceCode = useSpaceCodeSafe();
    const {caseEvaluateRecords} = useCaseEvaluateList();
    const [, , groupInfo] = useGroupStageInfo();
    const taskID = useTaskTaskID();
    const stageType = useCaseStageType();
    const [strategyList] = useStrategyTaskList();
    const strategyMetricList = useMemo(
        () => {
            return head(strategyList)?.metric ?? [];
        },
        [strategyList]
    );
    const [memoryHistoryScoreList, setMemoryHistoryScoreList] = useState<
        ScoreItem[]
    >([]);
    const isGSBStrategy = taskInfo?.manualPolicy === GSB_MANUAL_POLICY_ID;
    const evaluateOnceSplitterWidth = useEvaluateOnceSplitterWidth();
    useEffect(
        () => {
            let currentScore: ScoreItem[] | undefined = recordItem.origin.score;
            if (
                [TaskStageEnum.AUDITING, TaskStageEnum.AUDITING_FORWARD].includes(
                    stageType
                )
            ) {
                if (groupInfo.status !== CaseStatusEnum.RESOLVE_DISPUTE) {
                    currentScore =
                        recordItem?.diff?.score ?? recordItem.origin.score;
                }
            }
            const result =
                currentScore
                ?? strategyMetricList.map(({desc, metric}) => ({
                    desc,
                    metric,
                    scoreName: '',
                    score: null,
                }));
            setMemoryHistoryScoreList(result);
        },
        [groupInfo.status, stageType, recordItem, strategyMetricList]
    );

    const disabled = useMemo(
        () => taskInfo?.stage === 'TERMINATED',
        [taskInfo?.stage]
    );

    // TRICK wyq_prompt项目，用户不想打分，前端提供默认值
    useEffect(
        () => {
            const initScore = async () => {
                if (
                    spaceCode === 'wyq_prompt'
                    && stageType === TaskStageEnum.EVALUATING
                    && recordItem?.origin?.score?.some(item => item.score === null)
                ) {
                    try {
                        await apiCaseEvaluateUpsert({
                            accepted: 0,
                            predictRecordID: recordItem.origin.predictRecordID,
                            stageID: groupCaseInfo.stageID,
                            groupID: groupCaseInfo.groupID,
                            caseID: groupCaseInfo.caseID,
                            taskID: taskID,
                            score: strategyMetricList.map(
                                ({desc, metric, choices}) => ({
                                    desc,
                                    metric,
                                    scoreName: head(choices)?.name ?? '',
                                    score: head(choices)?.score ?? 0,
                                })
                            ),
                        });
                    } catch (error) {
                        /* empty */
                    } finally {
                        onRefresh?.();
                    }
                }
            };
            initScore();
        },
        [
            groupCaseInfo.caseID,
            groupCaseInfo.groupID,
            groupCaseInfo.stageID,
            stageType,
            onRefresh,
            recordItem.origin.ID,
            recordItem.origin.evaluateCaseID,
            recordItem.origin.predictRecordID,
            recordItem.origin.score,
            spaceCode,
            strategyMetricList,
            taskID,
        ]
    );

    const blockHandle = useMemo(
        () => {
            return (
                groupInfo.status === CaseStatusEnum.FINISH
                || [TaskStageEnum.FEEDBACK, TaskStageEnum.AUTO_EVALUATE].includes(stageType)
                || (stageType === TaskStageEnum.EVALUATING
                    && groupInfo.status === CaseStatusEnum.REJECTED)
                || ((groupCaseInfo.status === CaseStatusEnum.FINAL_DISPUTE
                    || recordItem.origin.status === CaseStatusEnum.FINAL_DISPUTE)
                    && stageType !== TaskStageEnum.ACCEPTING)
                || ([TaskStageEnum.AUDITING, TaskStageEnum.AUDITING_FORWARD].includes(
                    stageType
                )
                    && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE
                    && recordItem.origin.status === CaseStatusEnum.SUBMIT)
            );
        },
        [
            groupCaseInfo.status,
            groupInfo.status,
            stageType,
            recordItem.origin.status,
        ]
    );

    const needUpdateRecord = useMemo(
        () => {
            return (
                ([TaskStageEnum.AUDITING].includes(stageType)
                    && groupInfo.status === CaseStatusEnum.RESOLVE_DISPUTE)
                || stageType === TaskStageEnum.ACCEPTING
            );
        },
        [groupInfo.status, stageType]
    );

    const handleAutoUpdateSort = useCallback(
        async (updateMetric: string, updateScore: number) => {
            // 非当前操作
            const isNotOperationRecords = caseEvaluateRecords?.filter(
                i =>
                    i.origin.predictRecordID
                    !== recordItem.origin.predictRecordID
            );
            if (stageType === TaskStageEnum.EVALUATING || needUpdateRecord) {
                const {needSort, sortMetric} = head(strategyList);
                if (
                    !!needSort
                    && updateMetric === sortMetric
                    && caseEvaluateRecords
                    && isNotOperationRecords.every(
                        // todo 去除操作当条
                        item =>
                            item?.origin?.score
                            && item?.origin?.score?.some(
                                scoreItem =>
                                    scoreItem?.metric === sortMetric
                                    && scoreItem?.score !== null
                            )
                    )
                ) {
                    const sortList = caseEvaluateRecords
                        .map(record => ({
                            id: record.origin.predictRecordID,
                            score:
                                record.origin.predictRecordID
                                    === recordItem.origin.predictRecordID
                                    ? updateScore
                                    : record.origin.score.find(
                                        (item: any) =>
                                            item?.metric === sortMetric
                                    ).score,
                        }))
                        .sort((a, b) => b.score - a.score);

                    const newRanking = sortList.map(item => item.id);
                    const current = sortList.reduce(
                        (result: string[], curr, index, arr) => {
                            if (index > 0) {
                                if (curr.score > arr[index - 1].score) {
                                    result.push('<');
                                } else if (curr.score < arr[index - 1].score) {
                                    result.push('>');
                                } else {
                                    result.push('=');
                                }
                            }
                            return result;
                        },
                        []
                    );
                    await apiEvaluateCaseUpsert({
                        taskID,
                        stageID: groupCaseInfo.stageID,
                        caseID: groupCaseInfo.caseID,
                        groupID: groupCaseInfo.groupID,
                        ranking: newRanking,
                        operators: current,
                    });
                }
            }
        },
        [
            caseEvaluateRecords,
            groupCaseInfo.caseID,
            groupCaseInfo.groupID,
            groupCaseInfo.stageID,
            needUpdateRecord,
            recordItem.origin.predictRecordID,
            stageType,
            strategyList,
            taskID,
        ]
    );

    const handleChange = useCallback(
        async ({target}: any) => {
            if (blockHandle && stageType !== TaskStageEnum.FEEDBACK) {
                return;
            }
            try {
                const updateScore = target?.value;
                if (needUpdateRecord) {
                    await apiEvaluateRecordUpdate({
                        ID: recordItem.origin.ID,
                        predictRecordID: recordItem.origin.predictRecordID,
                        evaluateCaseID: recordItem.origin.evaluateCaseID,
                        note: updateScore,
                        taskID: taskID,
                        diff: [
                            {
                                type: CaseHistoryTypeEnum.RECORD,
                                caseID: groupCaseInfo.caseID,
                                groupID: groupCaseInfo.groupID,
                                recordID: recordItem.origin.predictRecordID,
                                field: 'note',
                                origin: `${recordItem.origin.note}`,
                                turnTo: `${updateScore}`,
                            },
                        ],
                    });
                } else {
                    await apiCaseEvaluateUpsert({
                        predictRecordID: recordItem.origin.predictRecordID,
                        stageID: groupCaseInfo.stageID,
                        groupID: groupCaseInfo.groupID,
                        caseID: groupCaseInfo.caseID,
                        taskID: taskID,
                        note: updateScore,
                        diff: [
                            {
                                type: CaseHistoryTypeEnum.RECORD,
                                caseID: groupCaseInfo.caseID,
                                groupID: groupCaseInfo.groupID,
                                recordID: recordItem.origin.predictRecordID,
                                field: 'note',
                                origin: `${recordItem.origin.note}`,
                                turnTo: `${updateScore}`,
                            },
                        ],
                    });
                }
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error, 'caseEvaluateUpsert error');
            } finally {
                onRefresh?.();
            }
        },
        [
            blockHandle,
            stageType,
            needUpdateRecord,
            recordItem.origin.ID,
            recordItem.origin.predictRecordID,
            recordItem.origin.evaluateCaseID,
            recordItem.origin.note,
            taskID,
            groupCaseInfo.caseID,
            groupCaseInfo.groupID,
            groupCaseInfo.stageID,
            onRefresh,
        ]
    );

    return (
        <div
            style={{
                minWidth:
                    layout === LayoutOptionEnum.Horizontal ? '530px' : '100%',
                flex: '1 0',
            }}
            id={`id${recordItem.origin.predictRecordID}`}
        >
            {layout === LayoutOptionEnum.Vertical ? (
                <Row
                    gutter={[8, 6]}
                    wrap={false}
                    id={`id${recordItem.origin.predictRecordID}`}
                >
                    <Col flex="auto">
                        <Splitter
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                            onResizeEnd={([x]) => {setEvaluateOnceSplitterWidth(x);}}
                        >
                            <Splitter.Panel
                                size={evaluateOnceSplitterWidth}
                                min="25%"
                                max="80%"
                                style={{overflow: 'hidden'}}
                            >
                                <FullScreenComponent
                                    title={
                                        <FlexLayout
                                            style={{width: '100%'}}
                                            justify="space-between"
                                            align="center"
                                        >
                                            <Space>
                                                {title}
                                                <AddTagPanel
                                                    caseID={groupCaseInfo.caseID}
                                                    stageID={groupCaseInfo.stageID}
                                                    refresh={onRefresh}
                                                    recordItem={recordItem.origin}
                                                />
                                            </Space>
                                            <FeedbackTagPanel
                                                refresh={onRefresh}
                                                recordItem={recordItem.origin}
                                                scoreList={memoryHistoryScoreList}
                                                isSingleRound
                                                blockHandle={blockHandle}
                                                disabled={disabled}
                                                needDivider
                                            />
                                        </FlexLayout>
                                    }
                                    content={recordItem.origin.output}
                                    reasoningContent={recordItem.origin?.reasoningContent}
                                    searchResults={recordItem.origin?.searchResults}
                                    showCharCount
                                    showDiff
                                    caseEvaluateItem={recordItem.origin}
                                    enableScratchWords
                                    recordID={recordItem.origin.predictRecordID}
                                    onRefresh={onRefresh}
                                    predictRecordID={
                                        recordItem.origin.predictRecordID
                                    }
                                />
                            </Splitter.Panel>
                            {!isGSBStrategy && (
                                <Splitter.Panel>
                                    <ScorePanel
                                        justify="space-around"
                                        direction="column"
                                        style={{
                                            height: '100%',
                                            background: '#fff',
                                            padding: 10,
                                        }}
                                    >
                                        <DiffAlertTitle
                                            recordItem={recordItem}
                                            groupCaseInfo={groupCaseInfo}
                                        />
                                        <Flex
                                            vertical
                                            style={{width: '100%'}}
                                            wrap="wrap"
                                            gap={8}
                                            align="start"
                                        >
                                            {strategyMetricList?.map(
                                                metricItem => {
                                                    return (
                                                        <ScoreGroup
                                                            key={metricItem.metric}
                                                            metricItem={metricItem}
                                                            groupInfo={groupInfo}
                                                            groupCaseInfo={
                                                                groupCaseInfo
                                                            }
                                                            recordItem={recordItem}
                                                            needUpdateRecord={
                                                                needUpdateRecord
                                                            }
                                                            handleAutoUpdateSort={
                                                                handleAutoUpdateSort
                                                            }
                                                            blockHandle={
                                                                blockHandle
                                                            }
                                                            handleRefresh={
                                                                onRefresh
                                                            }
                                                        />
                                                    );
                                                }
                                            )}
                                            <AutoComputePanel recordItem={recordItem} />
                                            <ScoreHistoryButton
                                                recordID={
                                                    recordItem.origin
                                                        .predictRecordID
                                                }
                                            />
                                            <Back2BackScoreButton
                                                predictRecordID={recordItem.origin.predictRecordID}
                                            />
                                        </Flex>
                                    </ScorePanel>
                                </Splitter.Panel>
                            )}
                        </Splitter>
                    </Col>
                    {remarkVisible && !isGSBStrategy && (
                        <Col flex={'0 0 300px'}>
                            <FlexLayout direction="column" gap={4} style={{width: '100%', height: '100%'}}>
                                <NotePanel blockHandle={blockHandle} recordItem={recordItem} />
                                <div
                                    style={{
                                        width: '100%',
                                        height: '150px',
                                        padding: '4px',
                                        opacity: '0.8',
                                        background: '#FFFFFF',
                                        borderRadius: '6px',
                                    }}
                                >
                                    <Input.TextArea
                                        style={{height: '100%', resize: 'none'}}
                                        name="note"
                                        defaultValue={recordItem.origin.note}
                                        draggable={false}
                                        maxLength={1000}
                                        bordered={false}
                                        placeholder="请输入备注"
                                        onChange={debounce(handleChange, 1000)}
                                        disabled={disabled}
                                    />
                                </div>
                            </FlexLayout>
                        </Col>
                    )}
                </Row>
            ) : (
                <FlexLayout
                    direction="column"
                    justify="space-between"
                    style={{height: '100%'}}
                    gap={2}
                >
                    <div style={{flex: '3 0 auto', width: '100%'}}>
                        <FullScreenComponent
                            title={
                                <FlexLayout
                                    style={{width: '100%'}}
                                    justify="space-between"
                                    align="center"
                                >
                                    {title}
                                    <FeedbackTagPanel
                                        refresh={onRefresh}
                                        recordItem={recordItem.origin}
                                        scoreList={memoryHistoryScoreList}
                                        isSingleRound
                                        blockHandle={blockHandle}
                                        disabled={disabled}
                                        needDivider
                                    />
                                </FlexLayout>
                            }
                            content={recordItem.origin.output}
                            reasoningContent={recordItem.origin?.reasoningContent}
                            searchResults={recordItem.origin?.searchResults}
                            showCharCount
                            showDiff
                            caseEvaluateItem={recordItem.origin}
                            enableScratchWords
                            recordID={recordItem.origin.predictRecordID}
                            onRefresh={onRefresh}
                            predictRecordID={recordItem.origin.predictRecordID}
                        />
                    </div>
                    {!isGSBStrategy && (
                        <ScorePanel
                            className={css`
                                background-image: url(${backgroundImage});
                                background-size: 100% 100%;
                            `}
                            justify="space-around"
                            direction="column"
                            gap={8}
                            style={{
                                width: '100%',
                                padding: '10px 20px',
                                flex: '0 0 auto',
                                borderRadius: '6px',
                            }}
                        >
                            <DiffAlertTitle
                                recordItem={recordItem}
                                groupCaseInfo={groupCaseInfo}
                            />
                            <Flex
                                vertical
                                style={{width: '100%'}}
                                wrap="wrap"
                                gap={8}
                                align="start"
                            >
                                {strategyMetricList?.map(metricItem => {
                                    return (
                                        <ScoreGroup
                                            key={metricItem.metric}
                                            metricItem={metricItem}
                                            groupInfo={groupInfo}
                                            groupCaseInfo={groupCaseInfo}
                                            recordItem={recordItem}
                                            needUpdateRecord={needUpdateRecord}
                                            handleAutoUpdateSort={
                                                handleAutoUpdateSort
                                            }
                                            blockHandle={blockHandle}
                                            handleRefresh={onRefresh}
                                        />
                                    );
                                })}
                                <ScoreHistoryButton
                                    recordID={recordItem.origin.predictRecordID}
                                />
                                <Back2BackScoreButton
                                    predictRecordID={recordItem.origin.predictRecordID}
                                />
                            </Flex>
                            <AutoComputePanel recordItem={recordItem} />
                            <AddTagPanel
                                caseID={groupCaseInfo.caseID}
                                stageID={groupCaseInfo.stageID}
                                refresh={onRefresh}
                                recordItem={recordItem.origin}
                            />
                            <NotePanel blockHandle={blockHandle} recordItem={recordItem} />
                            <div
                                style={{
                                    width: '100%',
                                    height: '150px',
                                    padding: '4px',
                                    opacity: '0.8',
                                    background: '#FFFFFF',
                                    borderRadius: '6px',
                                }}
                            >
                                <Input.TextArea
                                    style={{height: '100%', resize: 'none'}}
                                    name="note"
                                    defaultValue={recordItem.origin.note}
                                    draggable={false}
                                    maxLength={1000}
                                    bordered={false}
                                    placeholder="请输入备注"
                                    onChange={debounce(handleChange, 1000)}
                                    disabled={disabled}
                                />
                            </div>
                        </ScorePanel>
                    )}
                    <AutoEvaluateScorePanel recordItem={recordItem} />
                </FlexLayout>
            )}
        </div>
    );
};
