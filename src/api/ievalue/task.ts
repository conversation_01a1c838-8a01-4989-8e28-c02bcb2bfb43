/* eslint-disable max-lines */
import {
    CaseStageNumStatusEnum,
    CaseStatusEnum,
} from '@/constants/ievalue/case';
import {
    CooperateTypeEnum,
    EvaluatePriorityEnum,
    TargetEnum,
    TaskEvaluateModeEnum,
    TaskPredictTypeEnum,
    TaskStageEnum,
    TaskStatusEnum,
    TemplateStageCodeEnum,
} from '@/constants/ievalue/task';
import {TaskPredictRoundEnum} from '@/constants/ievalue/task';
import {getApiToken} from '@/regions/ievalue/apiToken';
import {createOnceWebSocket} from '@/utils/createInterface/createOnceWebSocket';
import {APP_WEBSOCKET_PREFIX} from '@/constants/app';
import {TaskTagItem} from './tags';
import {createInterface} from './axios';
import {
    AutoComputeMetricItem,
    GroupCaseListResponse,
    GroupCaseRandomItem,
    IOption,
    MetricFormItemParamItem,
} from './case';
import {DisplayDebugItem, ModelItem} from './model';
import {Paginated, PromptVersionVersionItem} from './prompt-version';

export interface TaskItem {
    ID: number;
    name: string;
    operator: string;
    updateTime: string;
    createTime: string;
    target: TargetEnum;
    stage: TaskStageEnum;
    note: string;
    predictType: string;
    stageStatus: string;
    caseStageNum: any;
    spaceCode: string;
    templateID: number;
    onlyPredict?: number;
    userAuth?: string;
    dirID?: number;
    dirPath?: string;
    promptVersionInfo?: PromptVersionVersionItem[];
    showMethod: string;
    stageID?: number;
    predictRound?: TaskPredictRoundEnum;
    evaluatePriority?: EvaluatePriorityEnum;
}
export interface TaskListParams {
    name?: string;
    stage?: string;
    stageStatus?: string;
    creator?: string;
    owner?: string;
    target?: string;
    createTimeStart?: string;
    createTimeEnd?: string;
    order?: string;
    pn?: number;
    size?: number;
    spaceCode?: string;
    evaluateMode?: string;
    stageList?: string;
    showMethod?: string;
    models?: string;
    versionID?: number;
    targets?: string;
    promptIDs?: string;
    predictRound?: TaskPredictRoundEnum;
    policyID?: number;
    tagIDs?: string;
    stageTemplateList?: string;
    reviewer?: string;
    auditer?: string;
    dirID?: number;
    withUserAuth?: number;
}

export interface TaskListResult {
    list: TaskItem[];
    total: number;
}
// 任务List接口
export const apiTaskList = createInterface<TaskListParams, TaskListResult>(
    'GET',
    'task/list'
);

export interface UpdateTaskItemParams {
    ID: number;
    name?: string;
    deleted?: number;
    note?: string;
}

// 任务update接口
export const apiUpdateTaskPost = createInterface<
    UpdateTaskItemParams,
    TaskItem
>('POST', '/task/update');

interface CaseStatsDispatchItem {
    unFinishedNum: number;
    userName: string;
}
export interface TaskCaseStatsItem {
    num: number;
    stageID: number;
    stageName: string;
    stageType: TaskStageEnum;
    sequence: number;
    dispatch: CaseStatsDispatchItem[];
}

interface TaskCaseStatsResponse {
    [key: number]: TaskCaseStatsItem;
}

// case流转查询任务阶段状态接口
export const apiTaskCaseStatsGet = createInterface<
    {taskID: number},
    TaskCaseStatsResponse
>('GET', '/task/case/stats');

// 完成按照case级别流转的任务
export const apiTaskCaseTransferFinish = createInterface<
    {taskID: number},
    any
>('GET', '/task/case/finish');

interface TaskCaseListParams {
    stageID: number | null;
    taskID: number;
    pn: number;
    size: number;
    status?: string;
    tags?: string;
    username?: string;
    stageStatus?: string;
    feedbacks?: string;
    pass?: number;
    sampled?: number;
}

// case流转查询caseList接口
export const apiTaskCaseListGet = createInterface<
    TaskCaseListParams,
    GroupCaseListResponse
>('GET', '/task/case/list');

export interface TaskCaseUsernamesResponse {
    list: string[];
}
export const apiTaskCaseUsernamesGet = createInterface<
    {taskID: number},
    TaskCaseUsernamesResponse
>('GET', '/task/case/usernames');

export interface GroupStageItem {
    name: string;
    num: string;
    completedNum?: number;
    failedNum?: number;
}

export interface TaskGroupStageItem extends GroupStageItem {
    stage: number;
    role: string;
    featureStageNums?: GroupStageItem[];
}

export interface GroupItem {
    taskID: number;
    groupID: number;
    groupName: string;
    stageStatus: string;
    spaceCode: string;
    caseNum: number;
    caseStageNum: TaskGroupStageItem[];
    createTime: string;
    updateTime: string;
    stageName: string;
    stageID?: number;
    stage?: TaskStageEnum;
}

export interface TaskGroupListResponse {
    total: number;
    list: GroupItem[];
}

export interface TaskGroupListParams {
    taskID: number;
    stageID?: number;
    userName?: string;
    name?: string;
    status?: string;
    pn: number;
    size: number;
    stageName?: string;
    tags?: string;
}

export const apiTaskGroupListGet = createInterface<
    TaskGroupListParams,
    TaskGroupListResponse
>('GET', '/group/list');

export interface TaskTemplate {
    templateID: number;
    name: string;
    desc: string;
    spaceCode: string;
    creator: string;
    createTime: string;
    stages: StagesItem[];
    onlyPredict: number;
    target?: string;
    code: TemplateStageCodeEnum;
}
export interface StagesItem {
    sequence: number;
    stageType: string;
    stageName: string;
    params: StageParams;
}
export interface StageParams {
    name?: string;
    note?: string;
    planName?: string;
    spaceCode?: string;
    target?: string;
    modelDates?: ModelDatesItem[];
    predictType?: string;
    cooperateType?: string;
    evaluateMode?: string;
    policies?: number[];
}
export interface ModelDatesItem {
    maxTokens: number;
    modelID: number;
    frequencyPenalty: number;
    presencePenalty: number;
    stop: string;
    temperature: number;
    topP: number;
}

export interface TaskTemplateListParams {
    target?: string;
    stageType?: string;
    spaceCode?: string;
}
// 任务模板List接口
export const apiTaskTemplateList = createInterface<
    TaskTemplateListParams,
    TaskTemplate[]
>('GET', '/task/stage/template/list');

export interface DatasetsItem {
    dataSetID: number;
    name: string;
    url: string;
    spaceCode: string;
    upload_time: string;
    creator: string;
}

interface EvaluateParamItem {
    evaluateMode: TaskEvaluateModeEnum;
    cooperateType: CooperateTypeEnum;
    blind: number;
    priority: string;
    showMethod?: string;
    isRoundScoring?: number;
    isSampling?: number;
    samplingRatio?: number;
}

export interface BlindParamItem {
    sequence: string;
    stageName: string;
    stageType: string;
    admin: number;
    creator: number;
    editor: number;
}

export interface RejectRatioParamItem {
    sequence: string;
    stageName: string;
    stageType: string;
    rejectRatio: number;
}

export interface SamplingRatioParamItem {
    sequence: string;
    stageName: string;
    stageType: string;
    samplingRatio: number;
}

export interface ProportionParamItem {
    l1: string;
    samplingWeight: number;
}

export interface ZipFileItem {
    fileName: string;
    fileID: number;
}

export interface TaskInfoItem {
    trainTaskID?: number;
    isAutoTransfer?: boolean;
    taskID: number;
    planID: number;
    planName: string;
    spaceCode: string;
    name: string;
    target: TargetEnum;
    note: string;
    isRoundScoring: number;
    isSessionAutoScore: number;
    templateID: number;
    stage: TaskStageEnum;
    status: number;
    failMsg: string;
    creator: string;
    operator: string;
    modelParams: ModelParamsItem[];
    datasetID: number;
    mapTemplateID: number;
    datasetName: string;
    predictType: TaskPredictTypeEnum;
    predictRound: TaskPredictRoundEnum;
    evaluateParam: object;
    evaluateMode: TaskEvaluateModeEnum;
    cooperateType: CooperateTypeEnum;
    blind: number;
    blindParams: BlindParamItem[];
    priority: string;
    showMethod?: string;
    reportFormat?: string;
    datasetFormat?: string;
    isSampling?: number;
    samplingRatio?: number; // 废弃了，老数据里可能有
    samplingRateParams?: SamplingRatioParamItem[];
    withReject?: number;
    rejectRateParams?: RejectRatioParamItem[];
    proportion?: number;
    proportionParams?: ProportionParamItem[];
    createTime: string;
    updateTime: string;
    stageStatus: string;
    dispatchType: number;
    taskType?: string;
    onlyPredict?: number;
    ID?: number;
    spacePolicyID?: number;
    policy?: number;
    autoPolicy?: number;
    manualPolicy?: number;
    ordermadeParam?: any;
    predictParam?: any;
    hasFeedback?: number;
    isFast?: number;
    isSubdataCompleted?: number;
    secretLevel?: string;
    reportID?: string;
    deleted?: number;
    metric: MetricFormItemParamItem[];
    autoMetric: MetricFormItemParamItem[];
    manualMetric: MetricFormItemParamItem[];
    manualAutoComputeMetric: AutoComputeMetricItem[];
    datasetColumnMapID: number;
    promptHistoryID: string;
    promptVersionIDs?: number[];
    files: ZipFileItem[];
    lineUpInfo?: string;
    tags?: TaskTagItem[];
    dirID?: number;
    partner?: string;
}

export const apiTaskInfo = createInterface<{taskID: number}, TaskInfoItem>(
    'GET',
    '/task/info'
);

export const apiReordCardList = createInterface<{taskID: number}, any>(
    'GET',
    '/card_record/list'
);
export interface StageItem {
    stageType: TaskStageEnum;
    stageName: string;
    status: TaskStatusEnum;
    sequence: number;
    ID?: number;
    startTime?: string;
    endTime?: string;
}

export const apiTaskStageList = createInterface<
    {taskID: number},
    StageItem[]
>('GET', '/task/stage/list');

export interface CreateTaskItemParams {
    templateID: number;
    spaceCode: string;
    name: string;
    target: string;
    note: string;
    modelParams: ModelParamsItem[];
    datasetID: number;
    datasetName: string;
    predictType: string;
    evaluateMode: TaskEvaluateModeEnum;
    cooperateType: CooperateTypeEnum;
    blind: number;
    priority: string;
    showMethod?: string;
    isRoundScoring?: number;
    isSessionAutoScore?: number;
    isSampling?: number;
    samplingRatio?: number;
}
export interface ModelParamsItem {
    modelID: number;
    modelName: string;
    debugParams: DisplayDebugItem[];
    params: any;
}

interface CreateTaskItemResult {
    ID: number;
    taskID: number;
    baseParams: BaseParams;
    modelParams: ModelParamsItem[];
    datasets: number[];
    predictType: string;
    evaluateParams: EvaluateParamItem;
}
interface BaseParams {
    planName: string;
    spaceCode: string;
    name: string;
    target: string;
    note: string;
}

interface AssociateCardItemParams {
    taskID: number;
    title: string;
    cardID: string;
    cardType: string;
    space: string;
    status: string;
    sequence: number;
}

export const apiCreateTask = createInterface<
    CreateTaskItemParams,
    CreateTaskItemResult
>('POST', '/task/create/llm_task');

export const apiAssociateCard = createInterface<AssociateCardItemParams[], any>(
    'POST',
    '/card_record/associate'
);

export const apiDisassociateCard = createInterface<{ID: string}, any>(
    'GET',
    '/card_record/disassociate'
);
export const apiTerminateTask = createInterface<{taskID: number}, any>(
    'GET',
    '/task/terminate'
);

export interface CreatePromptTaskParams {
    dataset: number;
    evaluateMode: string;
    /**
     * 维度，评分策略信息
     */
    metric?: Metric[];
    name: string;
    note: string;
    policy?: number;
    promptHistoryIDs: number[];
    spaceCode: string;
    /**
     * 模板id
     */
    templateID: number;
    target: string;
    datasetColumnMapID: number;
}

export interface Metric {
    choices: Choice[];
    desc: string;
    metric: string;
}

export interface Choice {
    name: string;
    score: number;
}
export const apiCreatePromptTask = createInterface<
    CreatePromptTaskParams,
    CreateTaskItemResult
>('POST', '/task/create/prompt_task');

export interface CreatePromptTaskFastParams {
    promptHistoryIDs: number[];
    spaceCode: string;
    promptID: number;
    /**
     * 模板id
     */
    templateID: number;
    target: string;
    variables: any[];
    evaluateMode: string;
    optionalRaws: any[];
    headers: string[];
    multiModals: any[];
}
export const apiCreatePromptTaskFast = createInterface<
    CreatePromptTaskFastParams,
    CreateTaskItemResult
>('POST', '/task/create/prompt_task/fast');

export interface DatasetGroup {
    list: DatasetGroupItem[];
    total: number;
    dataSetID: number;
}
export interface DatasetGroupItem {
    subTaskName: string;
    predictRecordNum: number;
    caseStageNum: caseStageNumItem[];
}

export const apiDatasetGroup = createInterface<
    {datasetID: number, templateID?: number, sourceType?: string},
    DatasetGroup
>('GET', '/dataset/group/list');

export interface CaseDispatchItem {
    num: number;
    userName: string;
}
export interface DatasetCaseDispatchItem {
    dispatch: CaseDispatchItem[];
    stageName: string;
    stageType: TaskStageEnum;
    totalNum: number;
    sequence: number;
}
export interface DatasetCaseDispatchPreviewParams {
    datasetID: number;
    templateID?: number;
}
export interface DatasetCaseDispatchPreviewResponse {
    list: DatasetCaseDispatchItem[];
}
export const apiDatasetCaseDispatchPreview = createInterface<
    DatasetCaseDispatchPreviewParams,
    DatasetCaseDispatchPreviewResponse
>('GET', '/dataset/case/dispatch/preview');

interface DatasetCaseDispatchParams {
    datasetID: number;
    templateID: number;
    allowCaseRepeat: boolean;
    allowUserRepeat: boolean;
    caseIDs: number[];
    params?: {
        [key: string]: any;
    };
}

// case级别任务分配负责人
export const apiDatasetCaseDispatch = createInterface<
    DatasetCaseDispatchParams,
    void
>('POST', '/dataset/case/dispatch');

export const apiDatasetHeaderList = createInterface<
    {spaceCode: string, datasetID: number},
    {list: string[]}
>('GET', '/dataset/header/list');

export interface DatasetL1Params {
    datasetID: number;
    templateID: number;
    levelKey?: string;
}
export const apiDatasetL1 = createInterface<
    DatasetL1Params,
    {list: string[]}
>('GET', '/dataset/L1');

export interface TaskModelItem {
    taskID: number;
    modelID: number;
    maxTokens: number;
    temperature: number;
    topP: number;
    presencePenalty: number;
    frequencyPenalty: number;
    stop: string;
    model: string;
    modelType: string;
}

export const apiTaskModelList = createInterface<
    {taskID: number},
    TaskModelItem[]
>('GET', '/task/model/list');

export interface caseStageNumItem {
    name: string;
    role: TaskStageEnum;
    stage: string;
    stageID: number;
    status: CaseStageNumStatusEnum;
    statusNum: any[];
    desc?: string;
}
// 子任务详情
export interface TaskGroupInfoItem {
    groupID: number;
    groupName: string;
    stageName: string;
    stageID: number;
    groupStage: string;
    caseNum: number;
    caseStageNum: caseStageNumItem[];
    createTime: string;
    updateTime: string;
    status: CaseStatusEnum;
    stageStatus?: string;
    spaceCode: string;
    taskID: number;
}
export const apiTaskGroupInfo = createInterface<
    {taskID: number, groupID: number},
    TaskGroupInfoItem
>('GET', '/group/info');

export interface SubTaskAuth {
    groupID: number;
    stageID: number;
    userName: string;
}

// 子任务负责人更新
export const apiUpdateSubTaskAuth = createInterface<SubTaskAuth>(
    'POST',
    '/group/auth/update'
);

export const apiEvaluateFinish = createInterface<{groupID: number}>(
    'GET',
    '/evaluate/finish'
);

interface AuditFinishParams {
    groupID: number;
    pass: string;
}
const apiGetAuditFinish = createInterface<AuditFinishParams, string>(
    'GET',
    '/audit/finish'
);

export const apiAuditFinish = (params: AuditFinishParams) => {
    return apiGetAuditFinish(params, {
        headers: {
            'Api-Token': getApiToken() ?? 'null',
        },
    });
};

export const apiAcceptanceFinish = createInterface<{groupID: number}>(
    'GET',
    '/acceptance/finish'
);

export const apiAcceptanceCancel = createInterface<{groupID: number}>(
    'GET',
    '/acceptance/cancel'
);

export const apiPredictFinish = createInterface<{groupID: number}>(
    'GET',
    '/predict/finish'
);

export const apiAuditForwardFinish = createInterface<{groupID: number}>(
    'GET',
    '/audit_forward/finish'
);

export const apiAuditForwardArbitrationFinish = createInterface<{
    groupID: number;
}>('GET', '/audit_forward/arbitration/pass');
interface StageNameListResponse {
    list: string[];
}
export const apiStageNameList = createInterface<
    {taskID: number},
    StageNameListResponse
>('GET', '/task/stage/name/list');

export const apiAuditArbitrationPass = createInterface<
    {groupID: number},
    string
>('GET', '/audit/arbitration/pass');

export interface TaskGroupAuth {
    spaceCode: string | undefined;
    taskID: number | undefined;
    groupID: number | undefined;
    stageID: number | undefined;
}
// 查询子任务查询权限
export const apiCheckTaskGroupAuth = createInterface<TaskGroupAuth>(
    'GET',
    '/auth/group'
);

export interface CreateDatasetMapParams {
    spaceCode: string | undefined;
    querySort?: string;
    name?: string;
    line?: string;
    input?: string;
    stageOperator?: object;
    output?: object;
    referenceOutput?: string;
    outputList?: boolean;
    rolePlay?: string;
    referenceIndicator?: string;
    allModelID?: number[];
    templateID?: number;
}

export interface CreateShareTaskParams {
    taskID: number;
    groupID?: number;
    userName?: string;
    shareType: string;
}
export interface Outputs {
    promptID: string;
    versionID: string;
    output: string;
}
export interface Images {
    promptID: string;
    versionID: string;
    image: string;
}
export interface CreateDatasetMapPromptParams {
    datasetID: number;
    templateID?: number;
    groupName?: string;
    lineNum?: string;
    stageOperator?: object;
    variables?: IOption[];
    references?: References[];
    outputs?: Outputs[];
    promptVersionIDs?: number[];
    images?: Images[];
}
export interface References {
    promptID: string;
    versionID: string;
    reference: string;
}

export interface GetDatasetMapInfoParams {
    templateID: number;
    datasetID: number;
}

// 查询任务/子任务
export const apiGetShareList = createInterface<CreateShareTaskParams, any>(
    'GET',
    '/task/share/list'
);

// 分享任务/子任务
export const apiPostCreateShareTask = createInterface<
    CreateShareTaskParams,
    any
>('POST', '/task/share/create');

export const apiCreateShareTask = (params: CreateShareTaskParams) => {
    return apiPostCreateShareTask(params, {
        headers: {
            'Api-Token': getApiToken() ?? 'null',
        },
    });
};

// 当前项目数据集创建接口
export const apiCreateDatasetMap = createInterface<CreateDatasetMapParams, any>(
    'POST',
    '/dataset/map/create'
);

// prompt手工任务创建，当前项目数据集创建接口
export const apiCreateDatasetMapPrompt = createInterface<
    CreateDatasetMapPromptParams,
    any
>('POST', '/dataset/map/prompt/create');

interface GetDatasetMapInfoResponse {
    mapTemplateInfo: any;
    modelList: ModelItem[];
}

// 当前项目数据集详情接口
export const apiGetDatasetMapInfo = createInterface<
    GetDatasetMapInfoParams,
    GetDatasetMapInfoResponse
>('GET', '/dataset/map/info');

export interface AnalyzeDatasetParams {
    mapTemplateID?: number;
    datasetID: number;
    templateID: number;
    modelID?: number[];
    taskType?: string;
    promptVersionIDs?: number[];
    spaceCode?: string;
}

// 当前项目数据集List接口 (数据集解析容易超时,建议使用apiWsDatasetAnalyzeV1)
export const apiAnalyzeDataset = createInterface<AnalyzeDatasetParams, any>(
    'POST',
    '/dataset/analyze'
);
export const apiWsDatasetAnalyzeV1 = createOnceWebSocket<
    AnalyzeDatasetParams,
    string[]
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/dataset/map/analyze/v1`);

export interface CheckAnalyzeDatasetResponse {
    isValid: boolean;
    notice: string;
}

// 校验数据集是否可用
export const apiCheckAnalyzeDataset = createInterface<
    AnalyzeDatasetParams,
    CheckAnalyzeDatasetResponse
>('POST', '/dataset/analyze/check');

export interface IPromptInfo {
    taskID: number;
    planID: number;
    taskName: string;
    target?: string;
    url?: string;
    status: string;
    createTime: string;
    creator: string;
    operator: string;
    versionIdList: IVersionIdListItem[];
    colMap?: any;
    spaceCode: string;
    stage?: string;
}

export interface IVersionIdListItem {
    promptVersionID: number;
    promptID: number;
    promptName: string;
    desc: string;
    spaceCode: string;
    suiteID: number;
    creator: string;
    createTime: string;
    text: string;
    variables: Variable[];
    modelID: number;
    modelName: string;
    maxTokens: number;
    temperature: number;
    topP: number;
    frequencyPenalty: number;
    stop: string;
    label: string;
    commitID: string;
}

interface Variable {
    key: string;
    value: string;
}

export const apiOfflineTaskInfo = createInterface<
    {taskID: number},
    IPromptInfo
>('GET', '/offline/task/info');
export interface IPromptTaskItem {
    ID: number;
    datasetID: number;
    input: string;
    variables: IVariable[];
    output: string;
    versionID: number;
    commitID: string;
    status: string;
    startTime: string;
    endTime: string;
}

export interface IVariable {
    key: string;
    value: string;
}
export interface IOfflineTaskListResponse {
    total: number;
    finish: number;
    list: IPromptTaskItem[];
}

export interface IOfflineTaskListParams {
    taskID: number;
    stageID?: number;
    userName?: string;
    name?: string;
    status?: string;
    pn: number;
    size: number;
}
export const apiOfflineTaskList = createInterface<
    IOfflineTaskListParams,
    IOfflineTaskListResponse
>('GET', '/offline/task/list');
export interface ITasksParams {
    dataSetID: number;
    spaceCode?: string;
    taskName: string;
    versionIDList: number[];
    variableDatas: IVariableData[];
}

export interface IVariableData {
    key: string;
    value: string;
}

export const apiOfflineTaskDatasetCreateTasks = createInterface<
    ITasksParams,
    TaskInfoItem
>('POST', '/offline/task/dataset/createTasks');

export const apiOfflineTaskDownload = createInterface<{taskID: number}, any>(
    'GET',
    '/offline/task/download'
);

// 查询子任务查询权限
export const apiBatchFinishTask = createInterface<{groupIDs: string}, any>(
    'GET',
    '/acceptance/batchFinish'
);

// 任务模型推理日志查询
export const apiTaskModelLogInfo = createInterface<{taskID: number}, any>(
    'GET',
    '/task/model/log/info'
);

export const apiTaskPredictRetry = createInterface<
    {taskID: number, retryRange: string},
    any
>('POST', '/predict/retry');

export const apiTaskPredictCancel = createInterface<{taskID: number}, any>(
    'GET',
    '/predict/cancel'
);

export const apiTaskStageRollback = createInterface<
    {targetStageType: string, taskID: number},
    void
>('GET', '/task/stage/rollback');

export interface TaskQueueItem {
    ID: string;
    caseNum: number;
    creator: string;
    msg: string;
    name: string;
    priority: number;
    spaceName: string;
    createTime: string;
    done: number;
    total: number;
}

// 自动评估服务排队api
export const apiTaskEvaluatorQueueList = createInterface<
    {evaluatorID?: number},
    TaskQueueItem[]
>('GET', '/evaluate/evaluator/queueList');

// 取消自动评估任务api
export const apiTaskEvaluateAutoCancel = createInterface<
    {taskID: string, evaluatorID: number},
    any
>('GET', '/evaluate/auto/cancel');

interface EvaluatePriorityUpdateParam {
    priority: number;
    taskID: string;
    evaluatorID: number;
}

// 取消自动评估任务api
export const apiTaskEvaluatePriorityUpdate = createInterface<
    EvaluatePriorityUpdateParam,
    any
>('POST', '/evaluate/priority/update');

export const apiIcafeSpaceList = createInterface<{kwords: string}, any>(
    'GET',
    '/card/space/list'
);

interface IcafeSpaceCardListParam {
    pn: number;
    size: number;
    kwords?: string;
    prefixCode?: string;
}

export interface CardList {
    total: number;
    currentPage: number;
    cards: ICafeCardItem[];
}
export interface ICafeCardItem {
    [key: string]: any;
    id: string;
    title?: string;
    sequence?: number;
    status?: string;
    spacePrefixCode: string;
    createdTime: string;
    issueID: string;
    type: Type;
    createdUser: CreatedUser;
    responsiblePeople: CreatedUser[];
}

export interface CreatedUser {
    username: string;
}

export interface Type {
    localId?: number;
    name?: string;
}
export const apiIcafeSpaceCardList = createInterface<
    IcafeSpaceCardListParam,
    CardList
>('GET', '/card/list');
export const apiTaskPromptVersionList = createInterface<
    {taskID: number},
    PromptVersionVersionItem[]
>('GET', '/task/prompt/version/list');

export interface DelineateWordParams {
    chatRecordID?: number;
    delineateWordID: string;
    position: Position;
    predictRecordID: number;
    stageID: number;
    stageName: string;
    tagName: string;
}

export interface Position {
    hs: Hs;
}

export interface Hs {
    __isHighlightSource: {[key: string]: any};
    endMeta: EndMeta;
    extra: Extra;
    id: string;
    startMeta: StartMeta;
    text: string;
}

export interface EndMeta {
    parentIndex: number;
    parentTagName: string;
    textOffset: number;
}

export interface Extra {
    type: string;
    value: any;
}

export interface StartMeta {
    parentIndex: number;
    parentTagName: string;
    textOffset: number;
}

// 划词保存
export const apiDelineateWordTagCreate = createInterface<
    DelineateWordParams[],
    void
>('POST', '/delineate/word/tag/create');

// 划词删除
export const apiDelineateWordTagDelete = createInterface<
    {ID?: number, delineateWordID?: string},
    void
>('GET', '/delineate/word/tag/delete');

/**
 * 更新划词
 */
export const apiDelineateWordTagUpdate = createInterface<
    {position: Position, delineateWordID: string},
    void
>('POST', '/delineate/word/tag/update');

export interface DelineateWordItem {
    delineateWordID: string;
    position: Position;
    tagList: TagList[];
}
export interface TagList {
    creator: string;
    role: string;
    stageName: string;
    tags: TagItem[];
}

export interface TagItem {
    chatRecordID: number;
    creator: string;
    deleted: number;
    delineateWordID: string;
    ID: number;
    position: Position;
    predictRecordID: number;
    stageID: number;
    stageName: string;
    tagName: string;
}
// 单轮划词查询
export const apiDelineateWordTagList = createInterface<
    {predictRecordID: number},
    DelineateWordItem[]
>('GET', '/delineate/word/tag/list');
export interface MultiDelineateWordItem {
    chatRecordID: number;
    delineateWordTags: DelineateWordItem[];
}
export const apiMultiDelineateWordTagList = createInterface<
    {predictRecordID: number},
    MultiDelineateWordItem[]
>('GET', '/multi/delineate/word/tag/list');

// 任务模板
export interface TaskTemplateItem {
    ID?: number;
    spaceCode: string;
    name: string;
    params?: TaskInfoItem;
    parentSpaceCode?: string;
    parentID?: number;
}

export interface TaskTemplateParams {
    taskID?: number;
    trainTaskID?: number;
    relatedThirdParty?: string;
    templateID?: number;
    stageList?: string;
    ID?: number;
    spaceCode?: string;
    creator?: string;
    name?: string;
    models?: number;
    pn?: number;
    size?: number;
}

// 任务模板列表
export const apiTaskPlanTemplateList = createInterface<
    TaskTemplateParams,
    Paginated<TaskTemplateItem>
>('GET', '/task/template/list');

// 根据任务创建模板
export const apiTaskTemplateCreateByTask = createInterface<
    TaskTemplateParams,
    any
>('GET', '/task/template/createByTask');
// 创建模版
export const apiTaskTemplateCreate = createInterface<
    TaskTemplateItem,
    TaskTemplateItem
>('POST', '/task/template/create');

// 更新任务模板
export const apiTaskTemplateUpdate = createInterface<
    TaskTemplateItem,
    TaskTemplateItem
>('POST', '/task/template/update');

// 删除任务模板
export const apiTaskTemplateDelete = createInterface<TaskTemplateParams, any>(
    'DELETE',
    '/task/template/delete'
);

export enum ReportPassEnum {
    DEFAULT = 0,
    PASS = 1,
    FAIL = 2,
}

export interface TaskMergeTemplateItem {
    creator: string;
    evaluateMode: string;
    groupNum: number;
    ID: number;
    isTemplate: boolean;
    mappingID: number;
    name: string;
    predictRecordNum: number;
    showMethod: string;
    stage: string;
    status: string;
    updateTime: string;
    pass: ReportPassEnum;
}

// comateStack任务列表接口
export const apiTaskMergeTemplateList = createInterface<
    {trainTaskID: number},
    Paginated<TaskMergeTemplateItem>
>('GET', '/taskMergeTemplate/list');

interface TrainTaskTemplateMapCreateParam {
    trainTaskID: number;
    taskTemplateID: number;
}

interface TrainTaskTemplateMapItem {
    createTime: string;
    creator: string;
    mappingID: number;
    spaceCode: string;
    taskID: number;
    taskTemplateID: number;
    trainTaskID: number;
}

// comateStack 模板关联任务接口
export const apiTrainTaskTemplateMapCreate = createInterface<
    TrainTaskTemplateMapCreateParam,
    TrainTaskTemplateMapItem
>('POST', '/train/task/template/map/create');

interface CustomizationDuZhiLiaoProductItem {
    productLineChinese: string;
    productLineID: string;
}

// 电商评估 产品线列表
export const apiCustomizationDuZhiLiaoProducts = createInterface<
    null,
    CustomizationDuZhiLiaoProductItem[]
>('GET', '/customization/duzhiliao/products');

export interface CustomizationDuZhiLiaoTaskItem {
    author?: string;
    connID: string;
    connIDDiff: null;
    createTime: string;
    groupIDList: string;
    groupIDsPromptIDs: string;
    isDel: string;
    lastEditor?: string;
    productLineID: string;
    promptTemplateID?: string;
    reviewerNum: string;
    scoreTemplateID?: string;
    taskDesc: string;
    taskID: string;
    taskName: string;
    taskRecorderID: string;
    taskStatusCode: string;
    taskType: string;
    updateTime: string;
}
// 电商评估 评估任务列表
export const apiCustomizationDuZhiLiaoTasks = createInterface<
    {productlineID: number},
    CustomizationDuZhiLiaoTaskItem[]
>('GET', '/customization/duzhiliao/tasks');

interface CustomizationDuZhiLiaoProgressItem {
    completed: number;
    progressPercentage: number;
    total: number;
}

// 电商评估 评估进度查询
export const apiCustomizationDuZhiLiaoProgress = createInterface<
    {taskID: number},
    CustomizationDuZhiLiaoProgressItem
>('GET', '/customization/duzhiliao/progress');

interface CustomizationDuZhiLiaoCreateParam {
    target: string;
    name: string;
    templateID: number;
    evaluateParam: CustomizationDuZhiLiaoTaskItem;
    promptHistoryID: string;
}
// 电商评估 创建评估任务
export const apiCustomizationDuZhiLiaoCreate = createInterface<
    CustomizationDuZhiLiaoCreateParam,
    any
>('POST', '/customization/duzhiliao/create');

// 查询当前阶段是否盲评
export const apiTaskStageIsBlind = createInterface<
    {taskID: number, stageID: number},
    {needBlind: boolean}
>('GET', '/stage/isBlind');

export interface TemplateShareParams {
    templateID: number;
    spaceCode: string;
    isRelate?: boolean;
}

export const apiTemplateShareGet = createInterface<TemplateShareParams, void>(
    'GET',
    '/task/template/share'
);

export interface BatchInferenceTaskParams {
    marker?: string;
    modelID: number;
    pageReverse?: boolean;
    size?: number;
    status?: string[];
}

export interface BatchInferenceTaskItem {
    createTime: string;
    creator: string;
    description: string;
    endpoint: string;
    finishTime: string;
    fromIevalue: boolean;
    inputTokenUsage: number;
    name: string;
    outputTokenUsage: number;
    progress: number;
    runStatus: string;
    runStatusName: string;
    taskId: string;
}

interface BatchInferenceTaskPageInfo {
    isTruncated: boolean;
    marker: string;
    maxKeys: number;
    nextMarker: string;
}
interface BatchInferenceTaskResult {
    pageInfo: BatchInferenceTaskPageInfo;
    taskList: BatchInferenceTaskItem[];
}

interface BatchInferenceTaskResponse {
    result: BatchInferenceTaskResult;
}

export const apiBatchInferenceTasksPost = createInterface<
    BatchInferenceTaskParams,
    BatchInferenceTaskResponse
>('POST', '/batch/inference/tasks');

export interface SelectColumnGroupItem {
    name: string;
    columns: string[];
}

export interface CustomSelectColumnItem {
    uniqStr: string;
    list: string[];
    groupName: string;
    groupList: SelectColumnGroupItem[];
}

export const apiGroupFeatureUserList = createInterface<
    {taskID: number, groupID: number},
    string[]
>('GET', '/group/feature/user/list');

export interface GroupFeatureInfoItem {
    caseNum: number;
    caseStageNum: caseStageNumItem[];
    createTime: string;
    groupFeatureID: number;
    groupID: number;
    groupName: string;
    spaceCode: string;
    stageID: number;
    stageName: string;
    stageStatus: string;
    status: string;
    taskID: number;
    updateTime: string;
}

export interface GroupFeatureInfoParams {
    groupID: number;
    userName?: string;
    groupFeatureID?: number;
}

export const apiGroupFeatureInfo = createInterface<
    GroupFeatureInfoParams,
    GroupFeatureInfoItem
>('GET', '/group/feature/info');

export const apiEvaluateFeatureFinish = createInterface<{
    groupFeatureID: number;
}>('GET', '/evaluate/group/feature/finish');

export interface TaskPieChartParams {
    taskID?: number;
    stageName?: string;
    modelID: number;
}

export interface ScorePieChatItem {
    scoreName: string;
    scoreRatio: string;
}

export interface PieChartItem {
    desc: string;
    metric: string;
    scorePieChat: ScorePieChatItem[];
    ratedAvgScore?: number;
}

export const apiTaskPieChart = createInterface<
    TaskPieChartParams,
    PieChartItem[]
>('GET', '/task/pie/chart');

// 获取多轮turn推理进度信息
export const apiPredictChatRecordNum = createInterface<
    {groupID: number, taskID: number},
    {completedNum: number, total: number}
>('GET', '/predict/chat/record/num');

export interface ModelLineUpItem {
    model: string;
    modelID: number;
    modelName: string;
    waitingMinutes: number;
    caseCount: number;
    type: ModelLineUpItemTypeEnum;
}

export enum ModelLineUpItemTypeEnum {
    PAYMENT = 'payment',
    DEMO = 'demo',
}

// 获取模型排队信息
export const apiModelLineUpInfoPost = createInterface<
    {models?: string, promptHistoryIDs?: string, dataSetID?: number},
    ModelLineUpItem[]
>('POST', '/space/limit/model/public/info');

// 随机给未分配负责人任务分配负责人
export const apiTaskRandomAssignmentGet = createInterface<
    {stage: TaskStageEnum, dirID: number},
    number
>('GET', '/task/random/assignment');

// Case级别流转，随机给未分配负责人阶段分配负责人
export const apiCaseRandomAssignmentGet = createInterface<
    {stage: TaskStageEnum, taskID: number},
    GroupCaseRandomItem
>('GET', '/case/random/assignment');

export interface TaskConclusionItem {
    desc?: string;
    pass?: number;
    reason?: string;
    taskID?: number;
}

interface TaskRepeatConfirmResponse extends TaskConclusionItem {
    repeatConfirm: boolean;
}

// meg获取任务是否需要填写结论
export const apiTaskRepeatConfirm = createInterface<
    {taskID: number, stage?: TaskStageEnum},
    TaskRepeatConfirmResponse
>('GET', '/task/repeat/confirm');

// meg任务结论更新
export const apiTaskConclusionUpdatePost = createInterface<
    TaskConclusionItem,
    ModelLineUpItem[]
>('POST', '/task/conclusion/update');

interface stageNumItem {
    stage: TaskStageEnum;
    num: number;
}

interface DirTaskProcessListResponse {
    total: number;
    stageNum: stageNumItem[];
}

// 指定目录下任务进度展示
export const apiDirTaskProcessList = createInterface<
    {dirID: number},
    DirTaskProcessListResponse
>('GET', '/dir/task/process/list');

interface TaskScoreBasicItem {
    key: string;
    value: any;
}
export interface TaskScoreItem {
    basicInfo?: TaskScoreBasicItem[];
    choices?: Choice[];
    descript?: string;
    ID?: number;
    name?: string;
    score?: null;
    scoreName?: string;
    taskID?: number;
}

// 任务级打分项查询
export const apiTaskScoreListGet = createInterface<
    {taskID: number},
    TaskScoreItem[]
>('GET', '/task/score/list');

interface TaskScoreUpdateParams {
    ID: number;
    score: number;
    scoreName: string;
}

// 任务级打分项更新
export const apiTaskScoreUpdatePost = createInterface<
    TaskScoreUpdateParams,
    void
>('POST', '/task/score/update');

interface SpecialItem {
    isFileMapped: boolean;
    isFunctionMapped: boolean;
    isFunctionResultMapped: boolean;
    isImageMapped: boolean;
    isResponseFormatMapped: boolean;
}

// 判断任务是否有某些特殊参数
export const apiTaskMapHasFunctionFieldsPost = createInterface<
    {taskID: number},
    SpecialItem
>('GET', '/task/map/hasFunctionFields');

export interface MultiModalFeedbackParams {
    multiModalFeedbacks: Array<{
        abilityCategory: string;
        abilityChoices: string[];
    }>;
    otherRequirements: string;
    userProductInfo: string;
}
// 用户添加多模态数据反馈
export const apiMultiModalFeedbackAdd = createInterface<
    MultiModalFeedbackParams,
    void
>('POST', '/user/feedback/multi_modal/add');

export interface TaskHumanEfficiencyData {
    dirID: number;
    dirName: string;
    dirPath?: string;
    userName: string;
    reviewMinutes?: string;
    evaluateAccuracy: string;
    evaluationMinutes?: string;
    // No MEG
    taskEvaluatedCount?: number;
    taskReviewedCount?: number;
    taskUnreviewedCount?: number;
    caseEvaluatedCount?: number;
    caseReviewedCount?: number;
    caseUnreviewedCount?: number;
    // MEG
    agentEvaluatedCount?: number;
    agentReviewedCount?: number;
    agentUnreviewedCount?: number;
    deduplicatedAgentCount?: number;
    sessionEvaluatedCount?: number;
    sessionReviewedCount?: number;
    sessionUnreviewedCount?: number;
    errRecordCount?: number;
    reviewRecordCount?: number;
    rejectedAgentCount?: number;
    zeroSessionCount?: number;
}

export interface TaskHumanEfficiencyRequest {
    dirID?: number;
    dirIDs?: number[];
    spaceCode?: string;
    userFilter?: string;
    stageList?: string[];
    createTimeStart?: string;
    createTimeEnd?: string;
}

export const apiTaskGetHumanEfficiencyStats = createInterface<
    TaskHumanEfficiencyRequest,
    TaskHumanEfficiencyData[]
>('GET', '/dir/task/human/efficiency/stats');

export interface TaskStatisticalData {
    dirID: number;
    dirName: string;
    userName?: string;
    dirPath: string;
    evaluateAccuracy: string;
    evaluateMinutes?: string;
    reviewMinutes?: string;
    // No MEG
    taskEvaluatedCount?: number;
    taskReviewedCount?: number;
    taskTotalCount?: number;
    taskUnreviewedCount?: number;
    caseEvaluatedCount?: number;
    caseReviewedCount?: number;
    caseTotalCount?: number;
    caseUnreviewedCount?: number;
    // MEG
    agentEvaluatedCount?: number;
    agentReviewedCount?: number;
    agentTotalCount?: number;
    agentUnreviewedCount?: number;
    deduplicatedAgentCount?: number;
    sessionEvaluatedCount?: number;
    sessionReviewedCount?: number;
    sessionTotalCount?: number;
    sessionUnreviewedCount?: number;
    lowQualityLabels?: string;
    rejectAgentPercentage?: string;
    rejectedAgentCount?: number;
    zeroSessionCount?: number;
    zeroSessionPercentage?: string;
    errRecordCount?: number;
    reviewRecordCount?: number;
}
export interface TaskGetDirectoryEvaluationStatsParams {
    dirID?: number;
    dirIDs?: number[];
    spaceCode?: string;
    withSubDirs?: boolean;
    createTimeStart?: string;
    createTimeEnd?: string;
    evaluateTimeStart?: string;
    evaluateTimeEnd?: string;
    reviewTimeStart?: string;
    reviewTimeEnd?: string;
    stageList?: string[];
    stageStatus?: string;
    tagIDs?: string;
    reviewer?: string;
    reviewers?: string[];
    auditer?: string;
    auditors?: string[];
    target?: string;
    evaluateMode?: string;
    stageTemplateList?: string;
    policyID?: number;
    models?: string;
    predictRound?: TaskPredictRoundEnum;
    name?: string;
}
// 获取目标目录及目标目录下所有子目录的评估信息
export const apiTaskGetDirectoryEvaluationStats = createInterface<
    TaskGetDirectoryEvaluationStatsParams,
    TaskStatisticalData[]
>('GET', '/dir/task/evaluate/stats');

interface SpaceLimitTaskCheckRes {
    modelParams: ModelParamsItem[];
    haveQuota: boolean;
}

// 空间下任务用量限制检查
export const apiSpaceLimitTaskCheckPost = createInterface<
    any,
    SpaceLimitTaskCheckRes
>('POST', '/space/limit/task/check');

export interface ModelDisplayParam {
    name: string;
    description: string;
    type: string;
    value?: any;
    inUse?: boolean;
    inUseDisplay?: boolean;
}

export interface TaskModelDisplayItem {
    modelID: number;
    modelName: string;
    modelParams: ModelDisplayParam[];
}

export const apiTaskModelDisplayList = createInterface<
    {taskID: number},
    TaskModelDisplayItem[]
>('GET', '/task/model/display/list');

// 子任务用例初始化状态时间
export const apiGroupCaseStageTimeInitialize = createInterface<
    {caseID: number, stageID: number},
    void
>('GET', '/group/case/stage/time/initialize');

interface TaskInspectionThresholdRes {
    reached: boolean;
}

interface TaskInspectionThresholdParams {
    taskID: number;
    stageID?: number;
}

export const apiTaskInspectionThreshold = createInterface<
    TaskInspectionThresholdParams,
    TaskInspectionThresholdRes
>('GET', '/task/inspection/threshold');

// 批量流转单向审核案例
export const apiTaskAuditForwardBatchFinish = createInterface<
    TaskInspectionThresholdParams,
    void
>('GET', '/audit_forward/batch/finish');


export const apiTaskGetDirectoryEvaluationStatsWs = createOnceWebSocket<
    TaskGetDirectoryEvaluationStatsParams,
    TaskStatisticalData[]
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/dir/task/evaluate/stats`);

export const apiTaskGetHumanEfficiencyStatsWs = createOnceWebSocket<
    TaskHumanEfficiencyRequest,
    TaskStatisticalData[]
>(
    `${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/dir/task/human/efficiency/stats`
);

export interface TaskDetailRequest {
    dirID: number;
    spaceCode: string;
    drillDownType: string;
    reviewers?: string[];
    auditors?: string[];
    stageList?: string[];
    stageStatus?: string;
    evaluateTimeStart?: string;
    evaluateTimeEnd?: string;
    reviewTimeStart?: string;
    reviewTimeEnd?: string;
    createTimeStart?: string;
    createTimeEnd?: string;
}

export interface taskDetail {
    caseTotalCount: number;
    createTime: string;
    index: number;
    taskID: number;
    taskName: string;
    totalMinutes?: string;
    errRecordCount?: number;
    evaluateAccuracy?: string;
    reviewRecordCount?: number;
    // task
    lowQualityLabel?: string;
    zeroSessionCount?: number;
    zeroSessionPercentage?: string;
}

export interface HumanEfficiencyDetailData {
    avgTimeSpent?: number;
    maxTimeSpent?: number;
    medianTimeSpent?: number;
    minTimeSpent?: number;
    taskList: taskDetail[];
}

export interface taskDetailData {
    taskList: taskDetail[];
}

// 获取人效统计下钻信息
export const apiTaskGetHumanEfficiencyDetail = createOnceWebSocket<
    TaskDetailRequest,
    HumanEfficiencyDetailData
>(
    `${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/dir/task/human/efficiency/detail`
);

export const apiTaskGetTaskDetail = createOnceWebSocket<
    TaskDetailRequest,
    taskDetailData
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/dir/task/evaluate/detail`);

export interface TaskEvaluateInfo {
    accuracy: string;
    finishedTime: string;
    progressInfo: string;
    stageName: string;
    takeTime: string;
    username: string;
}

// 获取任务统计下钻列表内的单任务评估执行信息
export const apiTaskGetTaskEvaluateInfo = createOnceWebSocket<
    {taskID: number},
    TaskEvaluateInfo[]
>(`${APP_WEBSOCKET_PREFIX}/api/ievalue/api/v1/ws/dir/task/evaluate/info`);

export interface GetRecordModifyHistoryParam {
    taskID: number;
    recordID: number;
}
export interface RecordModifyHistoryItem {
    chatRecordID: number;
    createTime: string;
    groupID: number;
    ID: number;
    operator: string;
    origin: string;
    recordID: number;
    stageID: number;
    stageName: string;
    turnTo: string;
    type: string;
    updateTime: string;
}

// 查询当前模型结果修改记录
export const apiGetRecordModifyHistory = createInterface<
    GetRecordModifyHistoryParam,
    RecordModifyHistoryItem[]
>('GET', '/record/modify/histories');

interface TaskRejectThresholdParams {
    caseID: number;
    stageID: number;
    taskID: number;
}

export interface TaskAuditForwardRejectThresholdRes {
    auditee?: string;
    auditor?: string;
    ID: number;
    note: string;
    rejectReachedThreshold?: boolean;
    stageID: number;
    taskID: number;
    unAuditedCaseNum?: number;
}

// 检查是否达到拒绝阈值
export const apiTaskAuditForwardRejectThreshold = createInterface<
    TaskRejectThresholdParams,
    TaskAuditForwardRejectThresholdRes
>('GET', '/audit_forward/reject/threshold');

// 批量打回审核案例
export const apiTaskAuditForwardBatchReject = createInterface<
    TaskAuditForwardRejectThresholdRes,
    void
>('POST', '/audit_forward/batch/reject');

// 创建任务统计数据下钻详情 excel 生成任务
export const apiDirTaskInfoTempFileGenerate = createInterface<
    TaskDetailRequest,
    void
>('POST', '/dir/task/info/temp/file/generate');

