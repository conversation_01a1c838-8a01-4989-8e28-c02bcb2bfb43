/* eslint-disable max-lines */

export const MCP_SERVER_LIST = [
    {
        id: 1,
        name: 'Auth Service',
        description: 'Handles user authentication and authorization',
        serverType: 'RESTful API',
        serverProtocolType: 'SSE',
        status: 'draft',
        labels: [
            {
                id: 1,
                labelValue: 'security',
            },
            {
                id: 2,
                labelValue: 'authentication',
            },
        ],
        icon: '/icons/auth.png',
        modifiedTime: '2024-06-15T10:30:00Z',
        modifiedUser: 'admin',
    },
    {
        id: 2,
        name: 'Payment Gateway',
        description: 'Processes online payments',
        serverType: 'Microservice',
        serverProtocolType: 'SSE',
        status: 'draft',
        labels: [{
            id: 1,
            labelValue: 'finance',
        }, {
            id: 2,
            labelValue: 'transaction',
        }],
        icon: '/icons/payment.png',
        modifiedTime: '2024-06-14T14:22:15Z',
        modifiedUser: 'finance-team',
    },
    {
        id: 3,
        name: 'Notification Service',
        description: 'Sends push/email notifications',
        serverType: 'Message Queue',
        serverProtocolType: 'SSE',
        status: 'draft',
        labels: [
            {
                id: 3,
                labelValue: 'communication',
            },
            {
                id: 4,
                labelValue: 'notification',
            },
        ],
        icon: '/icons/notification.png',
        modifiedTime: '2024-06-12T09:45:30Z',
        modifiedUser: 'devops',
    },
    {
        id: 4,
        name: 'Analytics Engine',
        description: 'Processes user behavior data',
        serverType: 'Data Pipeline',
        serverProtocolType: 'SSE',
        status: 'release',
        labels: [
            {
                id: 5,
                labelValue: 'analytics',
            },
            {
                id: 6,
                labelValue: 'data',
            },
        ],
        icon: '/icons/analytics.png',
        modifiedTime: '2024-06-10T16:20:45Z',
        modifiedUser: 'data-team',
    },
    {
        id: 5,
        name: 'File Storage',
        description: 'Cloud file storage service',
        serverType: 'Object Storage',
        serverProtocolType: 'SSE',
        status: 'release',
        labels: [
            {
                id: 7,
                labelValue: 'storage',
            },
            {
                id: 8,
                labelValue: 'cloud',
            },
        ],
        icon: '/icons/storage.png',
        modifiedTime: '2024-06-08T11:15:20Z',
        modifiedUser: 'sysadmin',
    },
];

export const MCP_SERVER_TOOL_LIST = [
    {
        id: 111,
        name: 'Auth Service',
        description: 'Handles user authentication and authorization',
    },
    {
        id: 2,
        name: 'Payment Gateway',
        description: 'Processes online payments',
    },
];

export const MCP_APPLICATION = {
    'id': 111,
    'name': '智测师',
    'description': '',
    'modifiedTime': '',
    'modifiedUser': '',
    'serverConfig': '',
    'serverDetails': [{
        'id': 111,
        'name': 'ipipe',
        'description': 'test',
        'departmentName': '',
        'serverType': 'openapi', // 类型
        'serverProtocalType': 'sse',
        'status': 'draft',
        'enable': true,
        'labels': ['CI', 'CD'],
        'icon': '',
        'modifiedTime': '',
        'modifiedUser': '',
        'favorite': true,
        'params': [{
            'id': 111,
            'name': 'token',
            'description': '',
            'dataType': 'int',
            'required': true,
            'value': '',
        },
        {
            'id': 222,
            'name': 'token',
            'description': '',
            'dataType': 'int',
            'required': false,
            'value': '',
        }],
        'tools': [{
            'id': 111,
            'name': 'tool name',
            'description': 'tool desc',
            'status': 'developing',
        }],
    }],
};


export const MCP_MODEL_LIST = [{
    'id': 1206,
    'name': 'gpt-4o',
    'description': '',
    'tags': [] as string[],
    'hot': true,
}];

export const MCP_PLAYGROUND_CONFIG = {
    model: {
        id: 111,
        name: 'deepseek',
    },
    systemPrompt: 'test',
    mcpServers: [{
        id: 151,
        name: 'testopenapi',
        workspaceId: 15,
        serverKey: 'testopenapi',
        enable: true,
        offcialExample: true,
        // @ts-ignore
        serverParams: [],
        tools: [{
            id: 111,
            name: 'test',
            enable: true,
            toolKey: 'test',
            description: '',
        }],
    }],
};

export const MCP_PLAYGROUND_SERVER_CONFIG = {
    'mcpServers': {
        'f2c_mcp': {
            'timeout': 50,
            'url': 'https://f2c-mcp.yy.com/sse',
            'headers': {
                'mcp-session-id': '<-session->',
            },
        },
    },
};
