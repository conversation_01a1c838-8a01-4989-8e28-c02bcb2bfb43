import styled from '@emotion/styled';
import {Tabs, Tooltip} from 'antd';
import {useCallback, useMemo} from 'react';
import {setChat, useCurrentChat} from '@/regions/staff/chat';
import {Stage} from '@/components/Chat/Stage';
import {colors} from '@/constants/colors';
import {staffColors} from '@/constants/colors/staff';
import {ConversationIdProvider} from '@/components/Chat/Provider/ConversationIdProvider';
import {HeaderLeft} from './HeaderLeft';
import {HeaderRight} from './HeaderRight';

const Container = styled.div`
    height: 100vh;
    display: flex;
    flex-direction: column;
`;

const Header = styled.div`
    z-index: 2;
`;

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-nav-wrap {
        height: 52px;
        margin-left: 20px;
        .ant-5-tabs-tab {
            height: 52px;
            padding: 20px 0;
        }
        .ant-5-tabs-tab.ant-5-tabs-tab-active .ant-5-tabs-tab-btn {
            color: transparent;
            background: ${staffColors.primaryBg};
            background-clip: text;
        }
    }
    .ant-5-tabs-tab-btn {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #545454;
    }
    .ant-5-tabs-ink-bar {
        background: ${staffColors.primaryBg} !important;
    }

    background: ${colors.white};
`;

const TabItems = [{key: '概览', label: '概览'}, {key: '运行过程', label: '运行过程'}, {key: '结果预览', label: '结果预览'}];

export const SupportArea = () => {
    const {conversationId, currentStageId, stageIds} = useCurrentChat();

    const tabItems = useMemo(
        () => TabItems.map(item => {
            if (stageIds?.includes(item.key)) {
                return item;
            }
            return {
                ...item,
                label: (
                    <Tooltip title="任务还未完成，暂时无法查看">
                        <span>{item.label}</span>
                    </Tooltip>
                ),
                disabled: true,
            };
        }),
        [stageIds]
    );
    const handleChange = useCallback(
        (key: string) => {
            setChat(conversationId, item => ({
                ...item,
                currentStageId: key,
            }));
        },
        [conversationId]
    );

    const operations = {
        left: <HeaderLeft />,
        right: <HeaderRight />,
    };

    return (
        <ConversationIdProvider conversationId={conversationId}>
            <Container>
                <Header>
                    <StyledTabs
                        tabBarExtraContent={operations}
                        activeKey={currentStageId}
                        onChange={handleChange}
                        items={tabItems}
                    />
                </Header>
                <Stage />
            </Container>
        </ConversationIdProvider>
    );
};
