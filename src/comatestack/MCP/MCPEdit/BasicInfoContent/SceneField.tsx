/* eslint-disable max-lines */
import {Form} from 'antd';
import {Path} from '@panda-design/path-form';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import SceneSelect from './SceneSelect';

interface Props{
    path?: Path;
}

const SceneField = ({path = []}: Props) => {
    // 和表单相关的逻辑还是放在表单组件中比较合适，SceneSelect中只处理通用的组件逻辑
    // 如果是编辑MCP，会从url中获取workspaceId
    const spaceId = useMCPWorkspaceId();
    // 如果是新建MCP，用户在表单中自己选择空间
    const workspaceIdFromForm = Form.useWatch('workspaceId');
    const workspaceId = spaceId || workspaceIdFromForm;
    return (
        <Form.Item
            label="场景"
            name={[...path, 'labels']}
        >
            <SceneSelect workspaceId={workspaceId} />
        </Form.Item>
    );
};

export default SceneField;
