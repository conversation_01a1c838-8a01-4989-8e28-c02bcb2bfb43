/* eslint-disable max-lines */
import {Button, message, Modal} from '@panda-design/components';
import {Flex, Form, Tooltip, Typography} from 'antd';
import styled from '@emotion/styled';
import {useBoolean} from 'huse';
import {useCallback} from 'react';
import {isArray} from 'lodash';
import {IconInfoCircleSolid} from '@baidu/ee-icon';
import {IconPlus} from '@/icons/lucide';
import {Gap} from '@/design/iplayground/Gap';
import {IconSuccess} from '@/icons/status';
import {IconAlert, IconDelete, IconUnfold} from '@/icons/mcp';
import {MCPToolItem} from '@/types/mcp/mcp';
import {apiDeleteServerTool} from '@/api/mcp';
import {useMCPServerId} from '@/components/MCP/hooks';
import ToolAddModal from '../ToolAddModal';
import {useActiveTool} from '../hooks';
import {useMCPEditFormItem} from '../../Providers/MCPEditFormItemProvider';
import {useAddTool} from './hooks';

const Wrapper = styled(Flex)<{displayAll: boolean}>`
    min-width: 230px;
    flex-grow: 2;
    max-width: 230px;
    max-height: ${props => (props.displayAll ? '80vh' : '50vh')};
    overflow-y: auto;
`;

const ToolItem = styled.span<{active: boolean}>`
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 8px 12px;
    height: 40px;
    border-radius: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0px;
    background-color: ${props => (props.active ? '#E5F2FF80' : 'transparent')};
    &:hover{
        background-color: #E5F2FF80;
        button {
            display: block !important;
        }
    }
`;

const StyledStatusIcon = styled.span<{status: string}>`
    display: inline-flex;
    align-items: center;
    margin-right: 4px;
    color: ${props => (props.status === 'complete' ? '#00CC6D' : '#F58300')};
`;

const ShowMoreIconWithStatus = styled(IconUnfold) <{displayAll: boolean}>`
    width: 16px;
    height: 16px;
    transform: ${props => (props.displayAll ? 'rotate(0deg)' : 'rotate(-180deg)')};
`;

interface Props {
    displayAll: boolean;
    on: () => void;
    off: () => void;
}

const ToolListContent = ({displayAll, on, off}: Props) => {
    const {disabled: btnDisabled} = useMCPEditFormItem();
    const serverSourceType = Form.useWatch('serverSourceType');
    const mcpServerId = useMCPServerId();
    const [open, {on: show, off: hide}] = useBoolean();
    const [loading, {on: openLoading, off: offLoading}] = useBoolean();
    const {setFieldValue} = Form.useFormInstance();
    const tools: MCPToolItem[] = Form.useWatch('tools');
    const {activeToolIndex, setActiveToolIndex} = useActiveTool();
    const addTool = useAddTool();
    const handleClick = useCallback(
        (index: number) => {
            setActiveToolIndex(index);
        },
        [setActiveToolIndex]
    );
    const handleDelete = useCallback(
        async (index: number) => {
            Modal.confirm({
                content: '确定删除工具吗？',
                icon: <IconAlert />,
                onOk: async () => {
                    const toolId = tools[index]?.id;
                    if (toolId) {
                        try {
                            openLoading();
                            await apiDeleteServerTool({mcpServerId, toolId});
                            setActiveToolIndex(0);
                            setFieldValue('tools', tools.filter((_, i) => index !== i));
                            offLoading();
                            message.success('删除成功');
                        } catch (e) {
                            message.error('删除失败');
                        }
                    } else {
                        message.success('删除成功');
                        setActiveToolIndex(0);
                        setFieldValue('tools', tools.filter((_, i) => index !== i));
                    }
                },
            });
        },
        [mcpServerId, offLoading, openLoading, setActiveToolIndex, setFieldValue, tools]
    );
    return (
        <Wrapper displayAll={displayAll} vertical>
            <ToolAddModal isLocal={serverSourceType === 'script'} open={open} onCancel={hide} onAdd={addTool} />
            {
                displayAll && (
                    <>
                        <Flex justify="space-between" align="center">
                            <h3 style={{margin: 0}}>工具</h3>
                            <Button
                                type="text"
                                disabled={false}
                                tooltip="收起工具"
                                onClick={displayAll ? off : on}
                                icon={<ShowMoreIconWithStatus displayAll={displayAll} />}
                            />
                        </Flex>
                        <Gap />
                    </>
                )
            }
            <Button onClick={show} icon={<IconPlus />} disabled={btnDisabled}>添加工具</Button>
            <Gap />
            <Flex vertical gap={8}>
                {(isArray(tools) ? tools : [])?.map((item, index: number) => (
                    <ToolItem active={index === activeToolIndex} onClick={() => handleClick(index)} key={index}>
                        <Flex gap={4}>
                            <StyledStatusIcon status={item?.toolStatus}>
                                <Tooltip title={item?.toolStatus === 'complete' ? '已发布' : '编辑中'}>
                                    {
                                        item?.toolStatus === 'complete' ? <IconSuccess /> : <IconInfoCircleSolid />
                                    }
                                </Tooltip>
                            </StyledStatusIcon>
                            <Typography.Text style={{maxWidth: 125}} ellipsis={{tooltip: true}}>
                                {item?.name}
                            </Typography.Text>
                        </Flex>
                        <Button
                            disabled={tools.length === 1 || btnDisabled}
                            onClick={e => {
                                e.stopPropagation();
                                handleDelete(index);
                            }}
                            loading={loading}
                            tooltip="删除工具"
                            type="text"
                            style={{display: 'none'}}
                            icon={<IconDelete />}
                        />
                    </ToolItem>
                ))}
            </Flex>
        </Wrapper>
    );
};

export default ToolListContent;

