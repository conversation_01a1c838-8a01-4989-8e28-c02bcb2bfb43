import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {Input, Select, Space, TableColumnsType} from 'antd';
import {useCallback} from 'react';
import {Path} from '@panda-design/path-form';
import {IconAdd} from '@/icons/lucide';
import {IconSubtract} from '@/icons/mcp';
import {RequiredTitle, StyledTable} from '../ParamList';
import {useMCPEditFormItem} from '../../Providers/MCPEditFormItemProvider';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const typeSelectOptions = [
    {label: 'String', value: 'string'},
    {label: 'Number', value: 'number'},
    {label: 'Boolean', value: 'boolean'},
    {label: 'Array', value: 'array'},
    {label: 'Object', value: 'object'},
    {label: 'Date', value: 'date'},
    {label: 'Time', value: 'time'},
];

interface Param {
    name: string;
    description: string;
    dataType?: string;
}

interface Props {
    path: Path;
    value?: Param[];
    onChange?: (value: Param[]) => void;
}

const ParamsListOfToolAdd = ({value, onChange, path}: Props) => {
    const {MCPEditFormItem, disabled: btnDisabled} = useMCPEditFormItem();
    const onAdd = useCallback(
        () => {
            onChange?.([...(value || []), {name: '', description: ''}]);
        },
        [onChange, value]
    );

    const onDelete = useCallback(
        (index: number) => {
            onChange?.([...value.slice(0, index), ...value.slice(index + 1)]);
        },
        [onChange, value]
    );

    const columns: TableColumnsType<Param> = [
        {
            title: <RequiredTitle>参数名称</RequiredTitle>,
            dataIndex: 'name',
            width: 200,
            render: (_, record, index) => (
                <Space>
                    <Button
                        icon={<IconSubtract />}
                        tooltip="删除"
                        type="text"
                        onClick={() => onDelete(index)}
                        disabled={btnDisabled}
                    />
                    <MCPEditFormItem
                        style={{marginBottom: 0}}
                        name={[...path, index, 'name']}
                        rules={[{required: true, message: '请输入参数名称'}]}
                    >
                        <Input placeholder="请输入参数名称" />
                    </MCPEditFormItem>
                </Space>
            ),
        },
        {
            title: '描述',
            dataIndex: 'description',
            render: (_, record, index) => (
                <MCPEditFormItem
                    style={{marginBottom: 0}}
                    name={[...path, index, 'description']}
                >
                    <Input placeholder="请输入描述" />
                </MCPEditFormItem>
            ),
        },
        {
            title: <RequiredTitle>类型</RequiredTitle>,
            dataIndex: 'dataType',
            width: 200,
            render: (_, record, index) => (
                <MCPEditFormItem
                    style={{marginBottom: 0}}
                    name={[...path, index, 'dataType']}
                    rules={[{required: true, message: '请选择类型'}]}
                >
                    <Select options={typeSelectOptions} allowClear placeholder="请选择类型" />
                </MCPEditFormItem>
            ),
        },

    ];
    return (
        <Space direction="vertical" style={{width: '100%'}}>
            <StyledButton type="text" icon={<IconAdd />} onClick={onAdd} disabled={btnDisabled}>添加参数</StyledButton>
            <StyledTable
                dataSource={value}
                pagination={{hideOnSinglePage: true}}
                columns={columns}
            />
        </Space>
    );
};

export default ParamsListOfToolAdd;

