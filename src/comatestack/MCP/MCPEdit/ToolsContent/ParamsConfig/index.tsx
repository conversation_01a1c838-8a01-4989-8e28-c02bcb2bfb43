/* eslint-disable max-lines */
import {Checkbox, Flex, Form, TableColumnsType, Tabs, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {keys, omit} from 'lodash';
import {IconDownSolid} from '@baidu/ee-icon';
import {IconSubtract} from '@/icons/mcp';
import {Gap} from '@/design/iplayground/Gap';
import {IconPlus} from '@/icons/lucide';
import {BaseParam} from '@/types/mcp/mcp';
import {StyledTable} from '../ParamList';
import {useActiveTool} from '../hooks';
import ParamValueField from './ParamValueField';

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-tab-active{
        .ant-5-tabs-tab-btn{
            color: #0083FF !important;
        }
    }
    .ant-5-tabs-tab{
        font-size: 14px;
        &:not(:first-child) {
            margin-left: 24px;
        }
    }
`;
const ItemWrapper = styled.div<{activeKey: string, deep?: number}>`
    position: relative;
    padding-left: ${props => (
        props.activeKey === 'body'
            ? 16 + props.deep * 12 + 'px'
            : '42px'
    )};
    .button-wrapper {
        position: absolute;
        top: -4px;
        left: -8px;
    }
`;

const StyledTriangle = styled(IconDownSolid) <{expanded?: boolean}>`
    color: #BFBFBF;
    position: absolute;
    left: 32px;
    top: 16px;
    z-index: 10;
    margin-right: 8px;
    cursor: pointer;
    transform: ${props => (props.expanded ? 'rotate(180deg)' : 'rotate(0deg)')};
`;

// TODO 这里的参数联动需要优化，而且目前无法比较方便地监听到参数增删这个动作的变化。
// ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
const ParamsConfig = () => {
    const [showRequired, setShowRequired] = useState(false);
    const [activeKey, setActiveKey] = useState<string>();
    const {setFieldValue} = Form.useFormInstance();
    const {activeToolIndex} = useActiveTool();
    const paramsList: BaseParam[] = Form.useWatch(['tools', activeToolIndex, 'toolParams', 'toolParams']);
    const path = useMemo(
        () => ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
        [activeToolIndex]
    );
    const source = Form.useWatch(path);
    const tabItems = useMemo(
        () => keys(source).map(key => ({
            label: key,
            value: key,
            key,
        })),
        [source]
    );
    const handleRelate = useCallback(
        (record: BaseParam, index: number) => {
            const key = record.key || activeKey + '.' + record.name;
            const recordValuePath = [
                ...path,
                activeKey,
                ...activeKey === 'body'
                    ? record.currentPath.reduce(
                        (acc, cur, index) => {
                            return [
                                ...acc,
                                ...index > 0 ? ['children', cur] : [cur],
                            ];
                        }, []
                    )
                    : [index],
                'value',
            ];
            if (paramsList?.some(item => item.refParam === key)) {
                setFieldValue(
                    recordValuePath,
                    ''
                );
                setFieldValue(
                    ['tools', activeToolIndex, 'toolParams', 'toolParams'],
                    paramsList?.filter(item => item.refParam !== key)
                );
            } else {
                setFieldValue(
                    recordValuePath,
                    record.value
                        ? record.value.startsWith('$')
                            ? record.value
                            : '$' + record.value
                        : '$' + record.name
                );
                setFieldValue(
                    ['tools', activeToolIndex, 'toolParams', 'toolParams'],
                    [
                        ...paramsList || [],
                        {
                            refParam: key,
                            ...omit(
                                record,
                                [
                                    'children',
                                    'currentPath',
                                    'key', 'properties',
                                    'x-iapi-ignore-properties',
                                    'x-iapi-orders',
                                    'x-iapi-refs',
                                ]
                            ),
                            name: record.value
                                ? record.value.startsWith('$')
                                    ? record.value.slice(1)
                                    : record.value
                                : record.name,
                            exampleValue: record.example,
                        },
                    ]
                );
            }
        },
        [activeKey, activeToolIndex, paramsList, path, setFieldValue]
    );
    useEffect(
        () => {
            if (!activeKey) {
                setActiveKey(tabItems?.[0]?.key);
            }
        },
        [activeKey, tabItems]
    );
    const columns = useMemo<TableColumnsType<BaseParam>>(
        () => [
            {
                title: '参数名',
                dataIndex: 'name',
                width: 200,
                render: (name: string, record, index) => (
                    <ItemWrapper activeKey={activeKey} deep={record?.key?.split('.')?.length}>
                        {
                            activeKey !== 'params'
                                // eslint-disable-next-line max-len
                                ? paramsList?.some(item => item.refParam === record?.key || item.refParam === (activeKey + '.' + record.name))
                                    ? (
                                        <span className="button-wrapper">
                                            <Button
                                                onClick={() => handleRelate(record, index)}
                                                type="text"
                                                tooltip="取消关联"
                                                icon={<IconSubtract />}
                                            />
                                        </span>
                                    )
                                    : (
                                        <span className="button-wrapper">
                                            <Button
                                                onClick={() => handleRelate(record, index)}
                                                type="text"
                                                tooltip="关联"
                                                icon={<IconPlus />}
                                            />
                                        </span>
                                    )
                                : null
                        }
                        {name}
                    </ItemWrapper>
                ),
            },
            {
                title: '类型',
                width: 60,
                dataIndex: 'type',
            },
            {
                title: '是否必须',
                width: 60,
                dataIndex: 'required',
                render: (required: boolean) => (required ? '是' : '否'),
            },
            {
                title: '说明',
                width: 150,
                dataIndex: 'description',
                render: description => (
                    <Typography.Paragraph ellipsis={{rows: 2, tooltip: {title: description}}}>
                        {description}
                    </Typography.Paragraph>
                ),
            },
            {
                title: '默认值',
                dataIndex: 'value',
                width: 150,
                render: (_, record, index: number) => (
                    <ParamValueField
                        basePath={[...path, activeKey]}
                        record={record}
                        index={index}
                        activeKey={activeKey}
                    />
                ),
            },
        ],
        [activeKey, handleRelate, paramsList, path]
    );

    return (
        <Flex vertical>
            <Flex justify="space-between">
                <StyledTabs activeKey={activeKey} onChange={setActiveKey} items={tabItems} />
                <Checkbox
                    checked={showRequired}
                    onChange={e => setShowRequired(e.target.checked)}
                    style={{alignItems: 'center'}}
                >
                    仅看必填项
                </Checkbox>
            </Flex>
            <Gap />
            <Form.Item name={path} style={{marginBottom: 0}}>
                <StyledTable
                    rowKey="key"
                    expandable={{
                        defaultExpandAllRows: true,
                        expandIcon: ({expanded, onExpand, record}) => {
                            if (record?.children) {
                                return (
                                    <span onClick={e => onExpand(record, e)}>
                                        <StyledTriangle expanded={expanded} />
                                    </span>
                                );
                            }
                        },
                    }}
                    dataSource={source?.[activeKey] || []}
                    columns={columns}
                    pagination={{hideOnSinglePage: true}}
                />
            </Form.Item>
        </Flex>
    );
};

export default ParamsConfig;

