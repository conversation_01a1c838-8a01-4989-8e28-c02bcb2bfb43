import {Path} from '@panda-design/path-form';
import {Flex, Form, Input} from 'antd';
import {useMCPEditFormItem} from '../Providers/MCPEditFormItemProvider';

interface Props {
    path: Path;
}

const ToolInfo = ({path}: Props) => {
    const {MCPEditFormItem} = useMCPEditFormItem();
    return (
        <>
            <Form.Item name={[...path, 'toolStatus']} hidden />
            <Form.Item name={[...path, 'toolKey']} hidden />
            <Form.Item name={[...path, 'id']} hidden />
            <Form.Item name={[...path, 'serverId']} hidden />
            <MCPEditFormItem
                name={[...path, 'name']}
                label="工具名称"
                rules={[{required: true, message: '请输入工具名称'}]}
            >
                <Input disabled={false} showCount maxLength={50} placeholder="请输入" />
            </MCPEditFormItem>
            <Flex gap={4}>
                <MCPEditFormItem
                    style={{flexGrow: 1}}
                    name={[...path, 'toolKey']}
                    label="工具标识"
                    rules={[{required: true, message: '请输入工具标识'}]}
                >
                    <Input showCount maxLength={50} placeholder="请输入" />
                </MCPEditFormItem>
                {/* <StyledButton type="text">一键生成</StyledButton> */}
            </Flex>
            <MCPEditFormItem
                name={[...path, 'description']}
                label="工具描述"
                rules={[{required: true, message: '请输入工具描述'}]}
            >
                <Input.TextArea rows={3} showCount maxLength={1000} placeholder="请输入" />
            </MCPEditFormItem>
        </>
    );
};

export default ToolInfo;
