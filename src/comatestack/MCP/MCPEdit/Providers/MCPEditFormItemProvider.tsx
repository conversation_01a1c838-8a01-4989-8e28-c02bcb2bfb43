import constate from 'constate';
import {Form, FormItemProps} from 'antd';
import {cloneElement, ReactElement, useCallback} from 'react';
import {NamePath} from 'antd/lib/form/interface';

interface Props{
  serverSourceType?: string;
  activeTab?: string;
  // 希望排除的字段名
  disableExclude?: Array<string | number | Array<string|number> | RegExp>;
}

// 如果name是数组，总会拼成字符串去match，number类型也会转为string去match
const match = (name: Array<string| number> | string | number, exclude: Props['disableExclude']): boolean => {
    if (exclude && exclude.length > 0) {
        const nameToMatch = Array.isArray(name) ? name.join('-') : name;
        return exclude.some(item => {
            if (item instanceof RegExp) {
                return item.test(String(nameToMatch));
            } else if (Array.isArray(item)) {
                const itemToMatch = Array.isArray(item) ? item.join('-') : item;
                return String(itemToMatch) === String(nameToMatch);
            }
            return String(item) === String(nameToMatch);
        });
    }
    return false;
};

// 用于向MCPEdit下的子表单提供禁用值。
export const [MCPEditFormItemProvider, useMCPEditFormItem] =
  constate(({serverSourceType, activeTab, disableExclude}: Props) => {
      const disabled = serverSourceType === 'external' && activeTab === 'tools';
      const shouldItemDisabled = useCallback(
          (name: NamePath) => {
              const childMatched = match(name, disableExclude);
              return disabled && !childMatched;
          },
          [disableExclude, disabled]
      );
      const MCPEditFormItem = useCallback(
          // 这里简化了children的类型，假设children一定是ReactElement，而不是简单类型，如string等，后续如果有其他类型需要处理，再修改这里
          (props: FormItemProps & { children: ReactElement}) => {
              const {children, ...resetProps} = props;
              const name: NamePath = props.name;
              const childDisabled = shouldItemDisabled(name);
              return (
                  <Form.Item {...resetProps}>
                      {cloneElement(children, {disabled: childDisabled})}
                  </Form.Item>
              );
          },
          [shouldItemDisabled]
      );
      return {
          MCPEditFormItem,
          shouldItemDisabled,
          disabled,
      };
  });
