import styled from '@emotion/styled';
import {Flex, List} from 'antd';
import {useEffect, useState} from 'react';
import {useSearchParams} from '@panda-design/router';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServerTools, useMCPServerTools} from '@/regions/mcp/mcpServer';
import MCPToolCard from './MCPToolCard';
import MCPToolDetail from './MCPToolDetail';

const Container = styled(Flex)`
    flex: 1;
`;

const Content = styled.div`
     ::-webkit-scrollbar {
        display: none;
    }
`;

const MCPTool = () => {
    const {toolId} = useSearchParams();
    const mcpServerId = useMCPServerId();
    const tools = useMCPServerTools(mcpServerId);

    useEffect(
        () => {
            if (mcpServerId) {
                loadMCPServerTools({mcpServerId});
            }
        },
        [mcpServerId]
    );

    const [activeToolId, setActiveToolId] = useState<number>(toolId ? Number(toolId) : undefined);

    useEffect(
        () => {
            if (tools?.length > 0 && activeToolId === undefined) {
                setActiveToolId(tools[0].id);
            }
        },
        [tools, activeToolId]
    );

    return (
        <Container gap={26}>
            <Content style={{flex: 1}}>
                <List
                    bordered
                    style={{borderRadius: 8}}
                    dataSource={tools}
                    renderItem={item => (
                        <MCPToolCard
                            key={item.id}
                            tool={item}
                            active={item.id === activeToolId}
                            onChange={setActiveToolId}
                        />
                    )}
                />
            </Content>
            <Content style={{flex: 2}}>
                <MCPToolDetail toolId={activeToolId} />
            </Content>
        </Container>
    );
};

export default MCPTool;
