import styled from '@emotion/styled';
import {Flex, Typography} from 'antd';
import {IconArrowRight} from '@baidu/ee-icon';
import {MCPToolItem} from '@/types/mcp/mcp';

const Description = styled(Typography.Paragraph)`
    max-height: 88px;
    overflow: auto;
    ::-webkit-scrollbar {
        background: transparent;
    }
`;

const Container = styled(Flex)<{active?: 'true'}>`
    cursor: pointer;
    padding: 16px 24px;
    border-width: 1px;
    border-style: solid;
    border-radius: ${({active}) => (active ? '6px' : '0px')};
    border-color: ${({active}) => (active ? '#0080FF' : 'transparent')};
    border-bottom-color: ${({active}) => (active ? '#0080FF' : '#D9D9D9')};
    background-color: ${({active}) => (active ? '#E5F2FF' : 'transparent')};
    position: relative;

    svg {
        display: none;
        position: absolute;
        right: 16px;
    }
    
    ${Description} {
        display: ${({active}) => (active ? 'block' : 'none')};
    }

    :hover {
        background-color: rgba(229, 242, 255, 0.5);

        ${Description} {
            display: block;
        }

        svg {
            display: inline-block;
        }
    }
        
    &:last-of-type {
        border-bottom-color: ${({active}) => (active ? '#0080FF' : 'transparent')};
    }
`;


interface Props {
    tool: MCPToolItem;
    active?: boolean;
    onChange: (toolId: number) => void;
}

export default function MCPToolCard({tool, active, onChange}: Props) {
    const handleClick = () => {
        onChange(tool.id);
    };
    return (
        <Container vertical gap={8} onClick={handleClick} active={active ? 'true' : undefined}>
            <Typography.Title level={4} style={{color: active ? '#0080FF' : '#181818'}} ellipsis>
                {tool.name}
            </Typography.Title>
            <Description>
                {tool.description || '暂无描述'}
            </Description>
            <IconArrowRight />
        </Container>
    );
}
