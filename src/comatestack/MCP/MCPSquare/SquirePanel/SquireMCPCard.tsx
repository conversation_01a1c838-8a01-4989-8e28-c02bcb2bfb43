import {Flex, Space, Divider, Typography} from 'antd';
import styled from '@emotion/styled';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {css} from '@emotion/css';
import {MCPDetailLink} from '@/links/mcp';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {MCPServerBase} from '@/types/mcp/mcp';
import {apiPostViewCount} from '@/api/mcp';
import {IconEye} from '@/icons/mcp';
import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import MCPCard from '@/design/MCP/MCPCard';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {overflowHiddenCss} from '@/styles/components';
import PublishInfo from '@/components/MCP/PublishInfo';
import ServerTypeTag from './ServerTypeTag';

const containerCss = css`
    padding: 16px 20px 12px;
`;

const Gray = styled.span`
    font-size: 12px;
    line-height: 20px;
`;

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}
const SquireMCPCard = ({server, refresh}: Props) => {
    const {
        id,
        workspaceId,
        name,
        departmentName,
        serverSourceType,
        serverProtocolType,
        description,
        labels,
        icon,
        viewCount,
        favorite,
        publishTime,
        publishUser,
    } = server;
    const navigate = useNavigate();

    const handleClick = useCallback(
        async () => {
            await apiPostViewCount({mcpServerId: id});
            navigate(MCPDetailLink.toUrl({mcpId: id}));
        },
        [navigate, id]
    );

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <ServerTypeTag style={{position: 'absolute', right: 0, top: 0}} type={serverSourceType} />
            <Flex gap={14} align="center">
                <MCPServerAvatar icon={icon} />
                <Flex vertical justify="space-between" style={{overflow: 'hidden'}} gap={4}>
                    <Typography.Title level={4} ellipsis>{name}</Typography.Title>
                    <Typography.Text
                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}
                    >
                        {departmentName || '暂无部门信息'}
                    </Typography.Text>
                </Flex>
            </Flex>
            <Typography.Paragraph
                type="secondary"
                ellipsis={{rows: 2, tooltip: description}}
                style={{margin: '16px 0 12px', height: 44}}
            >
                {description || '暂无描述'}
            </Typography.Paragraph>
            <Flex align="center" style={{overflow: 'hidden'}} gap={4}>
                <MCPServerProtocolTypeTag type={serverProtocolType} />
                <TagGroup
                    labels={labels.map(label => ({id: label.id, label: label.labelValue}))}
                    prefix={null}
                    style={{flexShrink: 1, overflow: 'hidden'}}
                    color="light-purple"
                    gap={4}
                />
            </Flex>
            <Divider style={{margin: '16px 0 8px'}} />
            <Flex justify="space-between" align="center" className={overflowHiddenCss}>
                <Flex align="center" gap={6} className={overflowHiddenCss}>
                    <Flex align="center" gap={4}>
                        <IconEye />{viewCount}
                    </Flex>
                    <PublishInfo username={publishUser} time={publishTime} />
                </Flex>
                <Gray />
                <Space size={4}>
                    <MCPCollectButton refresh={refresh} favorite={favorite} serverId={id} size="small" />
                    <MCPSubscribeButton
                        refresh={refresh}
                        showText
                        workspaceId={workspaceId}
                        id={id}
                        size="small"
                    />
                </Space>
            </Flex>
        </MCPCard>
    );
};

export default SquireMCPCard;
