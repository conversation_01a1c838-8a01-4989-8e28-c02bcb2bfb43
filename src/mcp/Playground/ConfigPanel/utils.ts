import {difference} from 'lodash';
import {PlaygroundConfig, PostPlaygroundConfigParams} from '@/api/mcp/playground';
import {MCPServerBase, MCPToolItem} from '@/types/mcp/mcp';
import {apiPostManyServerDetails} from '@/api/mcp/server';
import {apiGetMCPGlobalVars} from '@/api/mcp';
import {combineResponse} from '@/components/MCP/MCPToolDebug/ Providers/MCPServerConfigProvider';
import {MCPPlaygroundFormValues, MCPServerFieldValues, MCPServerParamsFieldValues, MCPToolFieldValues} from './types';

export const sanitizePlaygroundConfig = (config: Partial<PlaygroundConfig>): MCPPlaygroundFormValues => {
    return {
        systemPrompt: config.systemPrompt,
        mcpServers: config?.mcpServers?.map(server => ({
            id: server.id,
            name: server.name,
            workspaceId: server.workspaceId,
            serverKey: server.serverKey,
            enable: server.enable,
            offcialExample: server.offcialExample,
            serverParams: server.serverParams?.map(param => ({
                name: param.name,
                dataType: param.dataType,
                description: param.description,
                defaultValue: param.defaultValue,
                value: param.value,
                required: param.required,
            })) ?? [],
            tools: server.tools?.map(tool => ({
                id: tool.id,
                name: tool.name,
                enable: tool.enable,
                toolKey: tool.toolKey,
                description: tool.description,
            })) ?? [],
        })) ?? [],
        model: config?.model,
    };
};

export const transformFormValuesToPlaygroundConfig = (values: MCPPlaygroundFormValues): PostPlaygroundConfigParams => {
    return {
        model: values.model,
        systemPrompt: values.systemPrompt,
        mcpServers: values.mcpServers
            ?.filter(server => server.id)
            ?.map(
                server => ({
                    id: server.id,
                    name: server.name,
                    workspaceId: server.workspaceId,
                    serverKey: server.serverKey,
                    enable: server.enable,
                    tools: server.tools?.map(tool => ({
                        id: tool.id,
                        name: tool.name,
                        enable: tool.enable,
                        toolKey: tool.toolKey,
                        description: tool.description,
                    })) ?? [],
                })
            ) ?? [],
    };
};

export const sanitizeTool = (tool: MCPToolItem): MCPToolFieldValues => {
    return {
        id: tool.id,
        name: tool.name,
        enable: true,
        toolKey: tool.toolKey,
        description: tool.description,
    };
};

export const sanitizeServer = (
    server: MCPServerBase,
    enable: boolean | undefined = true
): Omit<MCPServerFieldValues, 'tools' | 'serverParams'> => {
    return {
        id: server.id,
        name: server.name,
        enable: enable,
        workspaceId: server.workspaceId,
        serverKey: server.serverKey,
        offcialExample: server.offcialExample,
    };
};

export const getMergedServerListFieldValues = async (
    prevServers: MCPServerFieldValues[],
    currentServerIds: number[]
): Promise<MCPServerFieldValues[]> => {
    const prevServerIds = prevServers?.map(server => server.id) ?? [];
    const diff = difference(currentServerIds, prevServerIds);

    if (diff.length === 0) {
        return prevServers;
    }

    const diffServers = await apiPostManyServerDetails({serverIdList: diff});
    // 批量查询新增的 Server 的 serverParams 个人配置值
    const serverParamsValues = await Promise.all(
        diffServers?.map(server => apiGetMCPGlobalVars({mcpServerId: server.id}))
    );

    return currentServerIds?.map(
        serverId => {
            const diffServer = diffServers?.find(item => item.id === serverId);
            // 新增的 Server 需要补充完整的参数和工具
            if (diffServer) {
                const diffServersPos = diffServers?.findIndex(item => item.id === serverId);
                // 查询新增的 square Server 的 serverParams 个人配置值
                const serverParamsValue = serverParamsValues?.[diffServersPos];
                const mergedServerParamsValue: MCPServerParamsFieldValues[] = combineResponse(
                    diffServer.serverParams ?? [],
                    serverParamsValue ?? []
                );
                const enableServer = diffServer.offcialExample
                    || !mergedServerParamsValue?.some(item => item.required && !item.value);
                return {
                    ...sanitizeServer(diffServer, enableServer),
                    serverParams: mergedServerParamsValue,
                    tools: diffServer?.tools.map(sanitizeTool),
                };
            }
            return prevServers?.find(item => item.id === serverId);
        }
    );
};
