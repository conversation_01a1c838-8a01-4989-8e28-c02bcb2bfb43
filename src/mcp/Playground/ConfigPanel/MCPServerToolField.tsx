import {Flex, Form, Switch, Tooltip, Typography} from 'antd';
import {NamePath} from 'antd/es/form/interface';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {overflowHiddenCss} from '@/styles/components';
import {MCPDetailLink} from '@/links/mcp';
import {IconDetail} from '@/icons/mcp';
import {MCPToolFieldValues} from './types';

const StyledFlex = styled(Flex)`
    width: 300px;
`;
interface Props {
    serverId: number;
    path: NamePath;
}
export default function MCPToolField({serverId, path}: Props) {
    const tool = Form.useWatch<MCPToolFieldValues>(['mcpServers', ...path]);

    const handleOnDetailClick = () => {
        window.open(
            MCPDetailLink.toUrl({mcpId: serverId, tab: 'tools', toolId: tool?.id}),
            '_blank'
        );
    };

    return (
        <StyledFlex align="center" justify="space-between" style={{padding: '8px 0px'}}>
            <Typography.Text
                type="secondary"
                ellipsis
                className={overflowHiddenCss}
            >
                {tool?.name}
            </Typography.Text>
            <Flex align="center" gap={4}>
                <Tooltip title="查看工具详情">
                    <Button
                        type="text"
                        size="small"
                        icon={<IconDetail />}
                        onClick={handleOnDetailClick}
                    />
                </Tooltip>
                <Form.Item
                    name={[...path, 'enable']}
                    valuePropName="checked"
                    noStyle
                >
                    <Switch size="small" />
                </Form.Item>
            </Flex>
        </StyledFlex>
    );
}
