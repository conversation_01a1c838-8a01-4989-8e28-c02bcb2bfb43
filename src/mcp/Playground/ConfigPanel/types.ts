/**
 * MCP服务器工具定义
 */
export interface MCPToolFieldValues {
    id: number;
    name: string;
    enable: boolean;
    toolKey: string;
    description: string;
}

export interface MCPServerParamsFieldValues {
    name: string;
    dataType: string;
    description: string | null;
    value: string | null;
    required: boolean;
    defaultValue?: string | null;
}

/**
 * MCP服务器定义
 */
export interface MCPServerFieldValues {
    id: number;
    name: string;
    workspaceId: number;
    serverKey: string;
    enable: boolean;
    offcialExample: boolean;
    serverParams: MCPServerParamsFieldValues[];
    tools: MCPToolFieldValues[];
}

/**
 *  Playground配置面板模型字段值
 */
export interface ModelFieldValues {
    id: number;
    name: string;
}

/**
 * Playground配置面板表单值
 */
export interface MCPPlaygroundFormValues {
    model: ModelFieldValues;
    systemPrompt: string;
    mcpServers: MCPServerFieldValues[];
}
