import {Button, message} from '@panda-design/components';
import {Flex, Form, Switch} from 'antd';
import {useState} from 'react';
import {css} from '@emotion/css';
import {MCPGlobalVarsProvider} from '@/components/MCP/MCPToolDebug/ Providers/MCPServerConfigProvider';
import {MCPServerConfigButton} from '@/components/MCP/MCPToolDebug/MCPServerConfigButton';
import {IconSetting} from '@/icons/mcp';
import {MCPServerConfigModalProvider} from '@/components/MCP/MCPToolDebug/ Providers/MCPServerConfigModalProvider';
import {MCPServerFieldValues} from './types';

const highlightedCss = css`
    background: #CCE5FF;
    svg {
        color: #0080FF;
    }
`;
interface Props {
    name: number | string;
    server: MCPServerFieldValues;
}
export default function MCPServerOperationField({name, server}: Props) {
    const form = Form.useFormInstance();
    const enable = server?.enable;
    const [highlighted, setHighlighted] = useState(false);

    const handleChangeEnable = (checked: boolean) => {
        if (server?.serverParams?.some(item => item.required && !item.value && checked)) {
            message.warning('请先进行服务配置后启用');
            form.setFieldValue(['mcpServers', name, 'enable'], false);
            setHighlighted(true);
            setTimeout(() => {
                setHighlighted(false);
            }, 3000);
        }
        else {
            form.setFieldValue(['mcpServers', name, 'enable'], checked);
        }
    };

    const handleSuccess = (serverParam: any) => {
        form.setFieldValue(['mcpServers', name, 'serverParams'], serverParam);
    };

    return (
        <Flex gap={4} align="center">
            {Boolean(server?.serverParams?.length) && (
                <MCPServerConfigModalProvider>
                    <MCPGlobalVarsProvider mcpId={server?.id} temporary={false}>
                        <MCPServerConfigButton onSuccess={handleSuccess}>
                            <Button
                                icon={<IconSetting />}
                                type="text"
                                className={highlighted ? highlightedCss : undefined}
                            />
                        </MCPServerConfigButton>
                    </MCPGlobalVarsProvider>
                </MCPServerConfigModalProvider>
            )}
            <Form.Item
                name={[name, 'enable']}
                noStyle
            >
                <Switch size="small" checked={enable} onChange={handleChangeEnable} />
            </Form.Item>
        </Flex>
    );
}
