import {Button, message} from '@panda-design/components';
import {Flex, Form, Typography} from 'antd';
import {useBoolean} from 'huse';
import {useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {MCPServerBase} from '@/types/mcp/mcp';
import {buttonString} from '@/styles/components';
import {IconPlaygroundConfig} from '@/icons/mcp';
import {IconWarningO} from '@/icons/status';
import MCPServerListModal from './MCPServerListModal';
import MCPServerField from './MCPServerField';
import {MCPServerFieldValues, MCPToolFieldValues} from './types';
import MCPServerListEmpty from './MCPServerListEmpty';
import {MCPServerListType} from './MCPServerListModal/ServerListTabs';
import {getMergedServerListFieldValues} from './utils';

const Wrapper = styled.div`
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    padding: 4px 0px;
`;

export default function MCPServerListField() {
    const [open, {on, off}] = useBoolean(false);
    const [listType, setListType] = useState<MCPServerListType>('square');

    const form = Form.useFormInstance();
    const mcpServers = Form.useWatch<MCPServerFieldValues[]>('mcpServers', form);

    // 新增的 Server 需要写入 serverParams 和 tools 参数
    // serverParams 有两部分，一部分是 Server 的 serverParams 配置，另一部分是用户对 serverParams 的配置
    const handleOk = async (servers: MCPServerBase[]) => {
        try {
            const mergedMcpServers = await getMergedServerListFieldValues(
                mcpServers ?? [],
                servers?.map(server => server.id) ?? []
            );
            form.setFieldValue('mcpServers', mergedMcpServers);
            form.submit();
            message.success('MCP Server添加成功');
        }
        catch (e) {
            message.error('MCP Server添加失败');
        }
    };

    const initialValue = useMemo(
        () => mcpServers?.map(server => server.id),
        [mcpServers]
    );

    const handleAdd = () => {
        on();
        setListType('example');
    };

    const handleConfig = () => {
        on();
        setListType('square');
    };

    const enabledTools = useMemo(
        () => mcpServers?.reduce(
            (acc, server) => {
                if (server.enable && server.tools?.length) {
                    acc.push(...server.tools.filter(tool => tool.enable));
                }
                return acc;
            },
            [] as MCPToolFieldValues[]
        ) ?? [],
        [mcpServers]
    );

    return (
        <>
            <Form.Item
                required
                labelCol={{span: 24}}
                label={(
                    <Flex align="center" justify="space-between" style={{width: '100%', marginRight: -10}}>
                        MCP Servers
                        <Button
                            type="default"
                            className={buttonString}
                            onClick={handleConfig}
                            icon={<IconPlaygroundConfig />}
                        >
                            选择
                        </Button>
                    </Flex>
                )}
            >
                <Form.List name="mcpServers">
                    {fields => (
                        fields.length === 0
                            ? <MCPServerListEmpty onAdd={handleAdd} />
                            : (
                                <>
                                    {enabledTools?.length > 20 && (
                                        <Flex
                                            align="center"
                                            gap={6}
                                            style={{color: 'var(--ant-5-color-warning)', marginBottom: 8}}
                                        >
                                            <IconWarningO />
                                            <Typography.Text type="warning">
                                                添加工具已超过20个，模型识别精准度可能下降
                                            </Typography.Text>
                                        </Flex>
                                    )}
                                    <Wrapper>
                                        {fields.map(field => <MCPServerField name={field.name} key={field.key} />)}
                                    </Wrapper>
                                </>
                            )
                    )}
                </Form.List>
            </Form.Item>
            <MCPServerListModal
                initialValue={initialValue}
                type={listType}
                open={open}
                onClose={off}
                onOk={handleOk}
            />
        </>
    );
}
