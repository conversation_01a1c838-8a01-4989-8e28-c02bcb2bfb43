import styled from '@emotion/styled';
import {Flex, Form, List, Popover, Tooltip, Typography} from 'antd';
import {overflowHiddenCss} from '@/styles/components';
import {IconOffcialExample} from '@/icons/mcp';
import MCPServerToolField from './MCPServerToolField';
import {MCPServerFieldValues, MCPToolFieldValues} from './types';
import MCPServerOperationField from './MCPServerOperationField';

const StyledFlex = styled(Flex)`
    width: 100%;
    overflow: hidden;
    border-bottom: 1px solid #D9D9D9;
`;

const Wrapper = styled.div`
    width: 100%;
    overflow: hidden;
    padding: 0px 12px;

    &:hover {
        background: #E5F2FF80;
    }
    &:last-of-type {
        ${StyledFlex} {
            border-bottom: none;
        }
    }
`;

const IconTry = styled(IconOffcialExample)`
    font-size: 24px;
`;
interface Props {
    name: number;
}
export default function MCPServerField({name}: Props) {
    const server = Form.useWatch<MCPServerFieldValues>(['mcpServers', name]);

    return (
        <>
            <Popover
                arrow={false}
                placement="rightTop"
                title={(
                    <Flex align="center" justify="space-between">
                        <Typography.Title level={5}>工具</Typography.Title>
                        <Typography.Text type="secondary" style={{fontWeight: 500, color: '#BFBFBF'}}>
                            已开启：{server?.tools?.filter(item => item.enable).length} / {server?.tools?.length}
                        </Typography.Text>
                    </Flex>
                )}
                content={(
                    <List
                        size="small"
                        bordered={false}
                        dataSource={server?.tools}
                        style={{maxHeight: 240, overflow: 'auto'}}
                        renderItem={(item: MCPToolFieldValues, i: number) => {
                            return (
                                <List.Item
                                    key={item.id}
                                    style={{padding: 0, border: 'none'}}
                                >
                                    <MCPServerToolField
                                        serverId={server.id}
                                        path={[name, 'tools', i]}
                                    />
                                </List.Item>
                            );
                        }}
                    />
                )}
            >
                <Wrapper>
                    <StyledFlex align="center" justify="space-between" style={{padding: '7px 0px'}}>
                        <Flex gap={4} className={overflowHiddenCss}>
                            <Typography.Text type="secondary" ellipsis>{server?.name}</Typography.Text>
                            {server?.offcialExample && <Tooltip title="官方试用"><IconTry /></Tooltip>}
                        </Flex>
                        <MCPServerOperationField name={name} server={server} />
                    </StyledFlex>
                </Wrapper>

            </Popover>
        </>
    );
}
