import {colors, myColors} from '@/constants/colors';

export const mcpPlaygroundStyles = {
    layout: {
        containerPadding: '80px 185px 80px 100px',
        stepDescriptionTop: '20px',
        stepDescriptionOffset: '32px',
    },

    icons: {
        stepIconSize: {
            width: 64,
            height: 64,
        },
        ellipsisIconSize: 72,
    },

    typography: {
        title: {
            fontWeight: 600,
            fontSize: '30px',
            lineHeight: '36px',
            color: colors['gray-9'],
        },
        subtitle: {
            fontWeight: 400,
            fontSize: '16px',
            lineHeight: '36px',
            color: myColors.darkGray,
            marginTop: '16px',
            marginBottom: '123px',
        },
        stepDescription: {
            fontSize: '16px',
            color: colors['gray-7'],
            lineHeight: 1.5,
        },
    },

    positioning: {
        stepDescriptions: [
            {
                left: '0px',
                top: '20px',
                whiteSpace: 'nowrap' as const,
                overflow: 'visible' as const,
            },
            {
                left: 'calc(33.33% - 32px)',
                top: '20px',
                whiteSpace: 'nowrap' as const,
                overflow: 'visible' as const,
            },
            {
                left: 'calc(66.66% - 32px)',
                top: '20px',
                whiteSpace: 'nowrap' as const,
                overflow: 'visible' as const,
            },
            {
                left: 'calc(100% - 64px)',
                top: '20px',
                whiteSpace: 'nowrap' as const,
                overflow: 'visible' as const,
            },
        ],
    },
} as const;

export const stepTexts = [
    '选择合适的模型',
    '输入系统提示词',
    '选择使用的MCP Server',
    '输入Query，即刻体验',
] as const;
