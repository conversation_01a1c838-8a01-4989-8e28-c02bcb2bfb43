import {useCallback, useEffect, useRef, useState, KeyboardEvent} from 'react';
import {Flex} from 'antd';
import {UI_DIMENSIONS} from '../constants';
import SendButton from './SendButton';
import {Container, InputComponent} from './styles';
import {
    ChatMessageInputProps,
    getInputRef,
    shouldBeMultiLine,
    createInputProps,
} from './utils';

const ChatMessageInput = ({
    disabled = false,
    disabledReason = '',
    onSend,
    onStop,
    isGenerating = false,
    placeholder = '输入消息，回车发送，Shift+回车换行...',
}: ChatMessageInputProps) => {
    const [content, setContent] = useState('');
    const [sending, setSending] = useState(false);
    const [isMultiLine, setIsMultiLine] = useState(false);
    const inputRef = useRef<HTMLTextAreaElement>(null);

    useEffect(
        () => {
            setIsMultiLine(shouldBeMultiLine(content));
        },
        [content]
    );

    const handleSend = useCallback(
        async () => {
            if (!content.trim() || disabled || sending) {
                return;
            }

            setSending(true);
            try {
                await onSend(content.trim());
                setContent('');
                setIsMultiLine(false);
            } catch (error) {
                console.error('发送消息失败:', error);
            } finally {
                setSending(false);
            }
        },
        [content, disabled, sending, onSend]
    );
    const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.metaKey && !e.ctrlKey && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    const canSend = content.trim() && !disabled && !sending && !isGenerating;

    const renderButton = () => {
        return (
            <SendButton
                canSend={canSend}
                isGenerating={isGenerating}
                sending={sending}
                onSend={handleSend}
                onStop={onStop}
            />
        );
    };

    return (
        <Container>
            {isMultiLine ? (
                <>
                    <Flex vertical align="flex-start" style={{padding: UI_DIMENSIONS.PADDING_MEDIUM}}>
                        <InputComponent
                            ref={ref => {
                                inputRef.current = getInputRef(ref);
                            }}
                            onChange={e => setContent(e.target.value)}
                            onKeyDown={handleKeyDown}
                            {...createInputProps(
                                content,
                                disabled,
                                disabledReason,
                                placeholder,
                                true
                            )}
                        />
                    </Flex>
                    <Flex justify="flex-end" style={{padding: '7px 12px'}}>
                        {renderButton()}
                    </Flex>
                </>
            ) : (
                <Flex align="center" style={{padding: UI_DIMENSIONS.PADDING_MEDIUM}}>
                    <InputComponent
                        ref={ref => {
                            inputRef.current = getInputRef(ref);
                        }}
                        onChange={e => setContent(e.target.value)}
                        onKeyDown={handleKeyDown}
                        {...createInputProps(
                            content,
                            disabled,
                            disabledReason,
                            placeholder,
                            false
                        )}
                    />
                    <Flex align="center" style={{marginLeft: '8px'}}>
                        {renderButton()}
                    </Flex>
                </Flex>
            )}
        </Container>
    );
};

export default ChatMessageInput;
