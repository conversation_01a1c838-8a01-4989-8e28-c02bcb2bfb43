import {Input} from 'antd';
import styled from '@emotion/styled';
import {
    UI_DIMENSIONS,
    UI_COLORS,
} from '../constants';

export const Container = styled.div`
    width: 100%;
    border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_LARGE}px;
    border: 1px solid #d9d9d9;
    height: 42px;
    min-width: 830px;
`;

export const InputComponent = styled(Input.TextArea, {
    shouldForwardProp: prop => prop !== 'isMultiLine',
})<{ isMultiLine: boolean }>`
    resize: none;
    flex: 1;
    font-size: ${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px;
    min-height: ${props => (
        props.isMultiLine
            ? '60px'
            : '32px'
    )};
    max-height: 118px;
    border: none !important;
    box-shadow: none;
    &:disabled {
        background: ${UI_COLORS.BACKGROUND_DISABLED} !important;
        color: ${UI_COLORS.TEXT_TERTIARY};
    }

    ::-webkit-scrollbar {
        width: ${UI_DIMENSIONS.SCROLLBAR_WIDTH}px;
    }

    ::-webkit-scrollbar-thumb {
        background: ${UI_COLORS.SCROLLBAR_THUMB};
        border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_SMALL}px;
    }
`;
