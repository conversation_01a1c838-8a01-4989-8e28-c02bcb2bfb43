import {Flex} from 'antd';
import {MCPChatMessage} from '@/types/mcp/mcp';
import {MessageProvider} from '@/components/Chat/Provider/MessageProvider';
import {IconMcpAvatar} from '@/icons/mcp';
import {
    UI_DIMENSIONS,
} from '../constants';
import {useAgentIdId} from '../AgentIdProvider';
import MessageBubbleContent from './MessageBubbleContent';
import {UserName} from './styles';

interface MessageItemProps {
    message: MCPChatMessage;
}

const MessageItem = ({message}: MessageItemProps) => {
    const isUser = message.role === 'USER' || (message as any).role === 'user';
    const messageKey = message.messageId || 'unknown';
    const agentId = useAgentIdId();
    return (
        <MessageProvider messageId={message.messageId} agentId={agentId}>
            <div key={messageKey}>
                <Flex
                    vertical
                    style={{
                        marginBottom: UI_DIMENSIONS.MARGIN_LARGE,
                        gap: UI_DIMENSIONS.MARGIN_LARGE,
                    }}
                >
                    {!isUser && (
                        <Flex align="center" gap={UI_DIMENSIONS.SPACING_LARGE}>
                            <IconMcpAvatar style={{fontSize: UI_DIMENSIONS.FONT_SIZE_XLARGE}} />
                            <UserName>AI助手</UserName>
                        </Flex>
                    )}
                    <MessageBubbleContent
                        message={message}
                        isUser={isUser}
                    />
                </Flex>
            </div>
        </MessageProvider>
    );
};

export default MessageItem;
