import {ChatAction, ToolElement} from './element';
import {StageDiff} from './stageDiff';
import {ChatStatus} from './status';

export interface StageStep {
    stepId: string; // 用于定位，可以从对话区跳转到对应位置
    status: ChatStatus; // 通用的状态
    title: string; // 标题
    elements?: ToolElement[];
    steps?: StageStep[];
}

export interface StageTab {
    tabId: string; // 用于定位，可以从对话区跳转到对应位置
    title: string; // 标题
    elements?: ToolElement[];
}

export interface StageCollapse {
    collapseId: string; // 用于定位，可以从对话区跳转到对应位置
    title: string; // 标题
    elements?: ToolElement[];
}

export type StageType = 'text' | 'iframe' | 'steps' | 'diff' | 'tabs';

export interface StageBase {
    id: string;
    name: string;
    messageId: string;
    actions?: ChatAction[];
}

export interface StageText extends StageBase {
    type: 'text';
    value: {
        elements: ToolElement[];
    };
}

export interface StageIframe extends StageBase {
    type: 'iframe';
    value: {
        iframe: string;
    };
}

export interface StageSteps extends StageBase {
    type: 'steps';
    value: {
        steps: StageStep[];
        summary: string;
    };
}

export interface Stagetabs extends StageBase {
    type: 'tabs';
    value: {
        tabs: StageTab[];
    };
}

export interface StageCollapses extends StageBase {
    type: 'collapses';
    value: {
        collapses: StageCollapse[];
    };
}

interface StageF2cTab {
    tabId: string;
    title: string;
    elements?: ToolElement[];
    diff?: StageDiff;
}
export interface StageF2c extends StageBase {
    type: 'f2c';
    value: {
        tabs: StageF2cTab[];
    };
}

export interface StageIapi extends StageBase {
    type: 'iapi';
    value: {
        taskId: number;
        workId: number;
    };
}

export type ChatStage =
    | StageText
    | StageIframe
    | StageSteps
    | StageDiff
    | Stagetabs
    | StageCollapses
    | StageF2c
    | StageIapi;
