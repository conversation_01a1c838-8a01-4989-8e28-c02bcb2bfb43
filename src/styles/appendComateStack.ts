import {injectGlobal} from '@emotion/css';

export const appendComateStack = () => injectGlobal`
    body {
        .ant-5-table {
            .ant-5-typography {
                margin-bottom: 0;
            }
        }

        .ant-5-table-wrapper .ant-5-table-thead > tr > th {
            border-color: #f2f2f2 !important;
            white-space: nowrap;
        }

        .ant-5-table-wrapper .ant-5-table-tbody > tr > td {
            vertical-align: middle;
        }

        .ant-5-btn-sm {
            font-size: 12px;
        }

        .ant-5-select-tree-treenode {
            margin-bottom: 0 !important;
            padding-top: 4px !important;
        }
    }
`;
