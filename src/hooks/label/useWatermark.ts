import {useEffect, useRef, useMemo, useCallback} from 'react';
import {useLocation} from 'react-router-dom';
import {APP_IS_EXTERNAL} from '@/constants/app';

interface WatermarkConfig {
    text?: string;
    username?: string;
    fontSize?: number;
    fontColor?: string;
    rotate?: number;
    gap?: [number, number];
    opacity?: number;
}

export const useWatermark = (config: WatermarkConfig = {}) => {
    const location = useLocation();
    const watermarkRef = useRef<HTMLDivElement | null>(null);
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    const {
        fontSize = 14,
        fontColor = 'rgba(0, 0, 0, 0.08)',
        rotate = -20,
        gap = [120, 80],
        opacity = 0.6,
    } = config;

    // 检查当前路径是否包含 /app/{projectID}/label/
    const shouldShowWatermark = !APP_IS_EXTERNAL && /^\/comatestack\/app\/[^/]+\/label/.test(location.pathname);

    const watermarkText = useMemo(
        () => {
            if (config.text) {
                return config.text;
            }

            const username = config.username ? `${config.username} ` : '';
            const currentTime = new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
            });

            return `${username}${currentTime}`;
        },
        [config.text, config.username]
    );

    const createWatermark = useCallback(
        () => {
            // 创建 canvas 元素
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                return '';
            }

            // 设置字体用于测量文本
            ctx.font = `${fontSize}px Arial`;
            const textWidth = ctx.measureText(watermarkText).width;
            const textHeight = fontSize;

            // 设置画布大小
            const [gapX, gapY] = gap;
            canvas.width = textWidth + gapX;
            canvas.height = textHeight + gapY;

            // 重新设置字体（canvas 大小改变后需要重新设置）
            ctx.font = `${fontSize}px Arial`;
            ctx.fillStyle = fontColor;
            ctx.globalAlpha = opacity;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 旋转并绘制文字
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            ctx.translate(centerX, centerY);
            ctx.rotate((rotate * Math.PI) / 180);
            ctx.fillText(watermarkText, 0, 0);

            return canvas.toDataURL();
        },
        [watermarkText, fontSize, fontColor, opacity, rotate, gap]
    );

    const updateWatermark = useCallback(
        () => {
            if (!shouldShowWatermark || !watermarkRef.current) {
                return;
            }

            const dataUrl = createWatermark();
            watermarkRef.current.style.backgroundImage = `url(${dataUrl})`;
        },
        [shouldShowWatermark, createWatermark]
    );

    useEffect(
        () => {
            if (!shouldShowWatermark) {
                // 如果不需要显示水印，清理已存在的水印
                if (watermarkRef.current) {
                    watermarkRef.current.remove();
                    watermarkRef.current = null;
                }
                if (timerRef.current) {
                    clearInterval(timerRef.current);
                    timerRef.current = null;
                }
                return;
            }

            // 创建水印容器
            if (!watermarkRef.current) {
                const watermarkDiv = document.createElement('div');
                watermarkDiv.style.cssText = `
                position: fixed;
                top: 47px;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                background-repeat: repeat;
            `;
                document.body.appendChild(watermarkDiv);
                watermarkRef.current = watermarkDiv;
            }

            // 初始化水印
            updateWatermark();

            // 清理函数
            return () => {
                if (timerRef.current) {
                    clearInterval(timerRef.current);
                    timerRef.current = null;
                }
            };
        },
        [shouldShowWatermark, updateWatermark]
    );

    // 组件卸载时清理
    useEffect(
        () => {
            return () => {
                if (watermarkRef.current) {
                    watermarkRef.current.remove();
                    watermarkRef.current = null;
                }
                if (timerRef.current) {
                    clearInterval(timerRef.current);
                    timerRef.current = null;
                }
            };
        },
        []
    );
};
