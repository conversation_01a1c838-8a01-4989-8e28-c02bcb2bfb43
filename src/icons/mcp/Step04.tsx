import type { SVGProps } from "react";
const SvgStep04 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 71 74"
        {...props}
    >
        <g filter="url(#step04_svg__a)">
            <path
                fill="url(#step04_svg__b)"
                d="M54.913 12.558a4 4 0 0 1 4.301 3.674l2.903 36.886a4 4 0 0 1-3.674 4.301L14.58 60.871a4 4 0 0 1-4.302-3.673L7.376 20.312a4 4 0 0 1 3.673-4.302z"
            />
        </g>
        <path
            fill="#fff"
            d="m14.279 31.566 39.877-3.138 1.647 20.935a3 3 0 0 1-2.755 3.226l-33.895 2.668a3 3 0 0 1-3.227-2.755z"
        />
        <path
            stroke="#DE83FF"
            strokeLinecap="round"
            strokeOpacity={0.3}
            strokeWidth={1.5}
            d="M21.809 39.637c4-.148 11.226-1.385 8.278-3.16-3.686-2.217-10.087-.71-3.87 1.81s11.543.095 13.458-1.06c1.916-1.153-.353-4.485-1.154-1.915-.476 1.528 5.116 1.27 7.595.907M22.25 45.714c.833-.666 2.9-1.8 4.5-1 2 1 5.5 1.5 6.5 1 .8-.4 2-1.5 2.5-2"
        />
        <path
            fill="#fff"
            d="m53.999 26.434-39.877 3.138-.392-4.984a3 3 0 0 1 2.755-3.226l33.895-2.668a3 3 0 0 1 3.226 2.756z"
        />
        <g filter="url(#step04_svg__c)">
            <mask
                id="step04_svg__d"
                width={27}
                height={21}
                x={39.509}
                y={45.786}
                fill="#000"
                maskUnits="userSpaceOnUse"
            >
                <path fill="#fff" d="M39.509 45.786h27v21h-27z" />
                <path d="M46.558 63.995q-.92 0-1.507-.063a8 8 0 0 1-1.277-.251q-.712-.21-1.15-.586-.44-.398-.733-1.13-.315-.712-.314-1.717V53.74q0-.963.293-1.695.292-.733.753-1.151.398-.357 1.089-.566.69-.23 1.38-.292.797-.042 1.466-.042.942 0 1.507.062.565.042 1.256.252.753.23 1.193.607.44.376.753 1.088.314.711.314 1.737v6.51q0 .962-.314 1.695-.292.732-.753 1.13-.42.356-1.151.586a5.6 5.6 0 0 1-1.36.272q-.587.063-1.445.063m.042-1.967q.88 0 1.256-.105.376-.126.502-.44.126-.313.126-1.025V53.53q0-.712-.126-1.026t-.502-.418q-.378-.126-1.256-.126-.9 0-1.298.126-.376.105-.502.418-.126.314-.126 1.026v6.928q0 .711.126 1.025.126.315.502.44.398.105 1.298.105m12.874-.984h-6.28v-2.407l5.589-8.435h3.453v8.644h1.466v2.198h-1.466v2.742h-2.762zm0-2.198V53.09l-3.663 5.756z" />
            </mask>
            <path
                fill="#DC7DFF"
                d="M46.558 63.995q-.92 0-1.507-.063a8 8 0 0 1-1.277-.251q-.712-.21-1.15-.586-.44-.398-.733-1.13-.315-.712-.314-1.717V53.74q0-.963.293-1.695.292-.733.753-1.151.398-.357 1.089-.566.69-.23 1.38-.292.797-.042 1.466-.042.942 0 1.507.062.565.042 1.256.252.753.23 1.193.607.44.376.753 1.088.314.711.314 1.737v6.51q0 .962-.314 1.695-.292.732-.753 1.13-.42.356-1.151.586a5.6 5.6 0 0 1-1.36.272q-.587.063-1.445.063m.042-1.967q.88 0 1.256-.105.376-.126.502-.44.126-.313.126-1.025V53.53q0-.712-.126-1.026t-.502-.418q-.378-.126-1.256-.126-.9 0-1.298.126-.376.105-.502.418-.126.314-.126 1.026v6.928q0 .711.126 1.025.126.315.502.44.398.105 1.298.105m12.874-.984h-6.28v-2.407l5.589-8.435h3.453v8.644h1.466v2.198h-1.466v2.742h-2.762zm0-2.198V53.09l-3.663 5.756z"
            />
            <path
                fill="#fff"
                d="m45.051 63.932.213-1.989zm-1.277-.251L43.21 65.6l.019.005.019.005zm-1.15-.586-1.343 1.483.02.018.02.017zm-.733-1.13 1.857-.743-.014-.033-.014-.032zm-.021-9.921-1.857-.743zm.753-1.151-1.334-1.49-.005.004-.006.006zm1.089-.566.58 1.915.026-.008.026-.01zm1.38-.292-.104-1.998-.038.002-.038.004zm2.973.02-.22 1.988.036.004.036.003zm1.256.252.584-1.913-.004-.002zm1.946 1.695-1.83.807zm0 9.942-1.838-.788-.01.022-.009.023zm-.753 1.13 1.295 1.524.006-.005.006-.005zm-1.151.586-.6-1.908-.008.003-.008.002zm-1.36.272-.13-1.996-.042.003-.042.005zm-.147-2.01.535 1.928.049-.014.048-.016zm.502-.439 1.857.743zm0-8.979 1.857-.742zm-.502-.418-.633 1.897.048.016.05.014zm-2.554 0 .535 1.927.034-.01.033-.01zm-.502 9.397 1.857-.742zm.502.44-.632 1.897.06.02.063.017zm1.256 2.072v-2c-.578 0-1.002-.02-1.294-.051l-.213 1.988-.213 1.989c.49.052 1.07.074 1.72.074zm-1.507-.063.213-1.989a6 6 0 0 1-.964-.191l-.526 1.929-.526 1.93c.542.147 1.074.254 1.59.31zm-1.277-.251.565-1.919c-.277-.081-.387-.163-.414-.186l-1.302 1.519-1.301 1.519c.559.479 1.216.788 1.888.986zm-1.15-.586 1.34-1.483c-.004-.005-.099-.095-.216-.39l-1.857.743-1.857.742c.272.682.666 1.345 1.247 1.871zm-.733-1.13 1.83-.808c-.07-.157-.144-.44-.144-.909h-4c0 .87.135 1.733.484 2.524zm-.314-1.717h2V53.74h-4v6.51zm0-6.509h2c0-.442.067-.746.15-.952l-1.857-.743-1.857-.743c-.308.77-.436 1.596-.436 2.438zm.293-1.695 1.857.743c.112-.28.21-.386.242-.414l-1.346-1.48-1.345-1.48c-.583.53-.987 1.192-1.265 1.888zm.753-1.151 1.334 1.49c-.015.013.06-.058.334-.141l-.58-1.914-.58-1.915c-.646.196-1.297.502-1.842.99zm1.089-.566.632 1.898q.48-.16.93-.199l-.181-1.992-.181-1.991a8 8 0 0 0-1.833.387zm1.38-.292.106 1.997q.752-.04 1.36-.04v-4q-.731 0-1.57.045zm1.466-.042v2c.595 0 1.014.02 1.286.05l.221-1.988.22-1.987a16 16 0 0 0-1.727-.075zm1.507.062-.148 1.995c.197.015.468.063.824.17l.58-1.913.58-1.915a8 8 0 0 0-1.688-.331zm1.256.252-.585 1.912c.329.1.454.194.476.213l1.302-1.518 1.301-1.519c-.563-.483-1.233-.794-1.91-1.001zm1.193.607-1.302 1.518s.024.02.066.08c.042.061.097.156.16.297l1.83-.807 1.83-.807c-.291-.659-.698-1.3-1.283-1.8zm.753 1.088-1.83.807c.068.153.144.44.144.93h4c0-.877-.133-1.748-.484-2.544zm.314 1.737h-2v6.51h4v-6.51zm0 6.51h-2c0 .426-.07.714-.152.907l1.838.788 1.839.788c.335-.784.475-1.626.475-2.484zm-.314 1.695L49.41 61.2c-.11.276-.2.356-.203.36l1.307 1.513 1.307 1.514c.61-.527 1.023-1.201 1.303-1.901zm-.753 1.13-1.296-1.524c-.01.01-.127.1-.455.202l.6 1.908.6 1.908c.648-.204 1.3-.505 1.846-.97zm-1.151.586-.616-1.903c-.34.11-.63.163-.874.18l.13 1.995.128 1.996a7.6 7.6 0 0 0 1.847-.365zm-1.36.272-.214-1.989a12 12 0 0 1-1.231.052v4c.614 0 1.172-.022 1.657-.074zM46.6 62.028v2c.599 0 1.256-.03 1.791-.178l-.535-1.927-.536-1.927c.046-.013.017 0-.133.012q-.203.019-.587.02zm1.256-.105.632 1.897c.702-.234 1.386-.741 1.727-1.594l-1.857-.743-1.857-.742c.074-.185.195-.358.352-.496.15-.131.29-.193.37-.22zm.502-.44 1.857.743c.235-.587.269-1.258.269-1.768h-4c0 .184-.009.302-.017.369l-.004.03a1 1 0 0 1 .038-.117zm.126-1.025h2V53.53h-4v6.928zm0-6.928h2c0-.51-.034-1.181-.27-1.769l-1.856.743-1.857.743c-.026-.065-.036-.108-.038-.117l.004.03c.008.067.017.186.017.37zm-.126-1.026 1.857-.742c-.354-.885-1.078-1.396-1.824-1.603l-.535 1.927-.536 1.927a1.2 1.2 0 0 1-.43-.234c-.181-.15-.312-.34-.389-.532zm-.502-.418.632-1.898c-.582-.194-1.284-.228-1.888-.228v4c.25 0 .431.01.554.021.138.014.141.026.07.002zM46.6 51.96v-2c-.624 0-1.322.036-1.9.218l.602 1.908.602 1.907c-.053.017-.032.003.114-.01.133-.014.324-.023.582-.023zm-1.298.126-.535-1.927c-.746.207-1.47.718-1.824 1.602l1.857.743 1.857.743a1.35 1.35 0 0 1-.388.532 1.2 1.2 0 0 1-.431.234zm-.502.418-1.857-.742c-.235.587-.269 1.258-.269 1.768h4c0-.184.009-.303.017-.37l.004-.03a1 1 0 0 1-.038.117zm-.126 1.026h-2v6.928h4V53.53zm0 6.928h-2c0 .51.034 1.18.269 1.768l1.857-.743 1.857-.742a1 1 0 0 1 .038.116l-.004-.03a3 3 0 0 1-.017-.37zm.126 1.025-1.857.743c.341.853 1.025 1.36 1.727 1.594l.632-1.897.633-1.898c.079.027.22.089.37.22.157.138.278.311.352.495zm.502.44-.509 1.934c.533.14 1.189.17 1.807.17v-4q-.396 0-.614-.02c-.158-.012-.202-.026-.175-.018zm14.172-.88h2v-2h-2zm-6.28 0h-2v2h2zm0-2.406-1.667-1.105-.333.502v.603zm5.589-8.435v-2h-1.074l-.593.895zm3.453 0h2v-2h-2zm0 8.644h-2v2h2zm1.466 0h2v-2h-2zm0 2.198v2h2v-2zm-1.466 0v-2h-2v2zm0 2.742v2h2v-2zm-2.762 0h-2v2h2zm0-4.94v2h2v-2zm0-5.756h2v-6.868l-3.688 5.794zm-3.663 5.756-1.687-1.074-1.957 3.074h3.644zm3.663 2.198v-2h-6.28v4h6.28zm-6.28 0h2v-2.407h-4v2.407zm0-2.407 1.668 1.104 5.588-8.434-1.667-1.105-1.667-1.105-5.589 8.435zm5.589-8.435v2h3.453v-4h-3.453zm3.453 0h-2v8.644h4v-8.644zm0 8.644v2h1.466v-4h-1.466zm1.466 0h-2v2.198h4v-2.198zm0 2.198v-2h-1.466v4h1.466zm-1.466 0h-2v2.742h4v-2.742zm0 2.742v-2h-2.762v4h2.762zm-2.762 0h2v-2.742h-4v2.742zm0-4.94h2V53.09h-4v5.756zm0-5.756-1.688-1.074-3.663 5.756 1.688 1.074 1.687 1.074 3.663-5.756zm-3.663 5.756v2h3.663v-4H55.81z"
                mask="url(#step04_svg__d)"
            />
        </g>
        <g filter="url(#step04_svg__e)">
            <rect
                width={32}
                height={9}
                x={30.25}
                y={5.214}
                fill="#fff"
                rx={3}
            />
        </g>
        <rect width={12} height={3} x={33.25} y={8.214} fill="#DE83FF" rx={1} />
        <rect width={6} height={3} x={46.25} y={8.214} fill="#DE83FF" rx={1} />
        <rect width={6} height={3} x={53.25} y={8.214} fill="#DE83FF" rx={1} />
        <g filter="url(#step04_svg__f)">
            <rect
                width={32}
                height={9}
                x={32.25}
                y={16.214}
                fill="#fff"
                rx={3}
            />
        </g>
        <rect
            width={26}
            height={3}
            x={35.25}
            y={19.214}
            fill="#DE83FF"
            fillOpacity={0.5}
            rx={1}
        />
        <path
            stroke="#DE83FF"
            strokeDasharray="2 1"
            d="M30.25 10.214c-1.333-1.5-5.1-4.2-9.5-3s-5.167 5.834-5 8"
        />
        <path
            stroke="url(#step04_svg__g)"
            strokeDasharray="2 1"
            d="M55.25 25.714c1 1.5 3.7 4.7 6.5 5.5s4.5.334 5 0"
        />
        <path
            stroke="#DE83FF"
            strokeDasharray="2 1"
            d="M9.25 54.214c-1 .5-3 .5-4 0-1.29-.645-1.667-2.5-2-3"
        />
        <defs>
            <filter
                id="step04_svg__a"
                width={62.768}
                height={56.339}
                x={3.362}
                y={8.545}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.192157 0 0 0 0 0.482353 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1090_2918"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1090_2918"
                    result="shape"
                />
            </filter>
            <filter
                id="step04_svg__c"
                width={36.125}
                height={29.773}
                x={34.577}
                y={44.222}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={3} />
                <feGaussianBlur stdDeviation={2.5} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0478431 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.15 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1090_2918"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1090_2918"
                    result="shape"
                />
            </filter>
            <filter
                id="step04_svg__e"
                width={40}
                height={17}
                x={26.25}
                y={2.214}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={1} />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.2 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1090_2918"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1090_2918"
                    result="shape"
                />
            </filter>
            <filter
                id="step04_svg__f"
                width={40}
                height={17}
                x={28.25}
                y={13.214}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={1} />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.2 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1090_2918"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1090_2918"
                    result="shape"
                />
            </filter>
            <linearGradient
                id="step04_svg__b"
                x1={25.132}
                x2={85.66}
                y1={-2.653}
                y2={17.043}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#B47AFF" />
                <stop offset={1} stopColor="#FF9ABA" />
            </linearGradient>
            <linearGradient
                id="step04_svg__g"
                x1={59.75}
                x2={60.75}
                y1={30.714}
                y2={31.214}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.8} />
                <stop offset={1} stopColor="#DE83FF" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgStep04;
