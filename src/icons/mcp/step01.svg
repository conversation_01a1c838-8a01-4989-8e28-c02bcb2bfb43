<svg width="73" height="73" viewBox="0 0 73 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1075_4463)">
<path d="M6.54948 17.4237C6.37606 15.2214 8.02081 13.2954 10.2231 13.122L48.1059 10.1389C50.3082 9.96551 52.2341 11.6103 52.4075 13.8126L55.8616 57.6768C56.035 59.8791 54.3903 61.805 52.188 61.9785L14.3052 64.9615C12.1029 65.135 10.177 63.4902 10.0036 61.2879L6.54948 17.4237Z" fill="url(#paint0_linear_1075_4463)" fill-opacity="0.9"/>
<rect x="6.49706" y="13.6594" width="45.5135" height="51.5135" rx="3.75676" transform="rotate(-4.50248 6.49706 13.6594)" stroke="url(#paint1_linear_1075_4463)" stroke-width="0.486487"/>
<rect x="11.5991" y="17.8572" width="37" height="43.3429" rx="2.11429" transform="rotate(-4.50248 11.5991 17.8572)" fill="url(#paint2_linear_1075_4463)"/>
</g>
<g filter="url(#filter1_d_1075_4463)">
<rect x="17.963" y="9.50317" width="23" height="7" rx="3.5" transform="rotate(-4.50248 17.963 9.50317)" fill="white"/>
<rect x="17.963" y="9.50317" width="23" height="7" rx="3.5" transform="rotate(-4.50248 17.963 9.50317)" fill="url(#paint3_linear_1075_4463)" fill-opacity="0.4"/>
</g>
<rect x="17.1467" y="24.614" width="12" height="12" rx="4" transform="rotate(-4.50248 17.1467 24.614)" fill="#54E3FF" fill-opacity="0.25"/>
<rect x="32.1003" y="23.4365" width="12" height="4" rx="2" transform="rotate(-4.50248 32.1003 23.4365)" fill="#54E3FF" fill-opacity="0.25"/>
<rect x="32.6499" y="30.4148" width="12" height="4" rx="2" transform="rotate(-4.50248 32.6499 30.4148)" fill="#54E3FF" fill-opacity="0.25"/>
<rect x="18.3242" y="39.5676" width="27" height="4" rx="2" transform="rotate(-4.50248 18.3242 39.5676)" fill="#54E3FF" fill-opacity="0.25"/>
<rect x="18.7952" y="46.5491" width="27" height="4" rx="2" transform="rotate(-4.50248 18.7952 46.5491)" fill="#54E3FF" fill-opacity="0.25"/>
<rect x="19.533" y="29.4417" width="8" height="2" rx="1" transform="rotate(-4.50248 19.533 29.4417)" fill="#4FC8FF"/>
<rect x="24.2821" y="26.0583" width="8" height="2" rx="1" transform="rotate(85.4975 24.2821 26.0583)" fill="#4FC8FF"/>
<g filter="url(#filter2_d_1075_4463)">
<mask id="path-12-outside-1_1075_4463" maskUnits="userSpaceOnUse" x="41.7622" y="46.2991" width="24" height="19" fill="black">
<rect fill="white" x="41.7622" y="46.2991" width="24" height="19"/>
<path d="M48.811 62.5084C48.1971 62.5084 47.6948 62.4874 47.3041 62.4456C46.9134 62.4037 46.4878 62.32 46.0273 62.1944C45.5529 62.0549 45.1692 61.8595 44.8762 61.6084C44.5831 61.3433 44.339 60.9665 44.1436 60.4781C43.9343 60.0037 43.8296 59.4316 43.8296 58.7619V52.2526C43.8296 51.6107 43.9273 51.0456 44.1227 50.5572C44.318 50.0688 44.5692 49.6851 44.8762 49.406C45.1413 49.1688 45.5041 48.9805 45.9645 48.8409C46.425 48.6874 46.8855 48.5898 47.3459 48.5479C47.8762 48.52 48.3645 48.506 48.811 48.506C49.439 48.506 49.9413 48.527 50.318 48.5688C50.6948 48.5967 51.1134 48.6805 51.5738 48.82C52.0762 48.9735 52.4738 49.1758 52.7669 49.427C53.0599 49.6781 53.311 50.0409 53.5203 50.5154C53.7296 50.9898 53.8343 51.5688 53.8343 52.2526V58.7619C53.8343 59.4037 53.7296 59.9688 53.5203 60.4572C53.325 60.9456 53.0738 61.3223 52.7669 61.5874C52.4878 61.8247 52.1041 62.02 51.6157 62.1735C51.1413 62.327 50.6878 62.4177 50.2552 62.4456C49.8645 62.4874 49.3831 62.5084 48.811 62.5084ZM48.8529 60.5409C49.439 60.5409 49.8576 60.506 50.1087 60.4363C50.3599 60.3526 50.5273 60.206 50.611 59.9967C50.6948 59.7874 50.7366 59.4456 50.7366 58.9712V52.0433C50.7366 51.5688 50.6948 51.227 50.611 51.0177C50.5273 50.8084 50.3599 50.6688 50.1087 50.5991C49.8576 50.5154 49.439 50.4735 48.8529 50.4735C48.2529 50.4735 47.8203 50.5154 47.5552 50.5991C47.3041 50.6688 47.1366 50.8084 47.0529 51.0177C46.9692 51.227 46.9273 51.5688 46.9273 52.0433V58.9712C46.9273 59.4456 46.9692 59.7874 47.0529 59.9967C47.1366 60.206 47.3041 60.3526 47.5552 60.4363C47.8203 60.506 48.2529 60.5409 48.8529 60.5409ZM60.3871 51.9177L56.6824 53.9688V51.3944L60.9731 48.7154H63.4429V62.2991H60.3871V51.9177Z"/>
</mask>
<path d="M48.811 62.5084C48.1971 62.5084 47.6948 62.4874 47.3041 62.4456C46.9134 62.4037 46.4878 62.32 46.0273 62.1944C45.5529 62.0549 45.1692 61.8595 44.8762 61.6084C44.5831 61.3433 44.339 60.9665 44.1436 60.4781C43.9343 60.0037 43.8296 59.4316 43.8296 58.7619V52.2526C43.8296 51.6107 43.9273 51.0456 44.1227 50.5572C44.318 50.0688 44.5692 49.6851 44.8762 49.406C45.1413 49.1688 45.5041 48.9805 45.9645 48.8409C46.425 48.6874 46.8855 48.5898 47.3459 48.5479C47.8762 48.52 48.3645 48.506 48.811 48.506C49.439 48.506 49.9413 48.527 50.318 48.5688C50.6948 48.5967 51.1134 48.6805 51.5738 48.82C52.0762 48.9735 52.4738 49.1758 52.7669 49.427C53.0599 49.6781 53.311 50.0409 53.5203 50.5154C53.7296 50.9898 53.8343 51.5688 53.8343 52.2526V58.7619C53.8343 59.4037 53.7296 59.9688 53.5203 60.4572C53.325 60.9456 53.0738 61.3223 52.7669 61.5874C52.4878 61.8247 52.1041 62.02 51.6157 62.1735C51.1413 62.327 50.6878 62.4177 50.2552 62.4456C49.8645 62.4874 49.3831 62.5084 48.811 62.5084ZM48.8529 60.5409C49.439 60.5409 49.8576 60.506 50.1087 60.4363C50.3599 60.3526 50.5273 60.206 50.611 59.9967C50.6948 59.7874 50.7366 59.4456 50.7366 58.9712V52.0433C50.7366 51.5688 50.6948 51.227 50.611 51.0177C50.5273 50.8084 50.3599 50.6688 50.1087 50.5991C49.8576 50.5154 49.439 50.4735 48.8529 50.4735C48.2529 50.4735 47.8203 50.5154 47.5552 50.5991C47.3041 50.6688 47.1366 50.8084 47.0529 51.0177C46.9692 51.227 46.9273 51.5688 46.9273 52.0433V58.9712C46.9273 59.4456 46.9692 59.7874 47.0529 59.9967C47.1366 60.206 47.3041 60.3526 47.5552 60.4363C47.8203 60.506 48.2529 60.5409 48.8529 60.5409ZM60.3871 51.9177L56.6824 53.9688V51.3944L60.9731 48.7154H63.4429V62.2991H60.3871V51.9177Z" fill="#4DBFFF"/>
<path d="M47.3041 62.4456L47.5171 60.457L47.3041 62.4456ZM46.0273 62.1944L45.463 64.1132L45.482 64.1187L45.5011 64.1239L46.0273 62.1944ZM44.8762 61.6084L43.5343 63.0914L43.5542 63.1094L43.5746 63.1269L44.8762 61.6084ZM44.1436 60.4781L46.0006 59.7354L45.9876 59.7029L45.9734 59.6709L44.1436 60.4781ZM44.1227 50.5572L42.2657 49.8144L42.2657 49.8144L44.1227 50.5572ZM44.8762 49.406L43.5426 47.9156L43.5367 47.9208L43.5308 47.9262L44.8762 49.406ZM45.9645 48.8409L46.5445 50.755L46.5709 50.747L46.597 50.7383L45.9645 48.8409ZM47.3459 48.5479L47.2408 46.5507L47.2028 46.5527L47.1649 46.5561L47.3459 48.5479ZM50.318 48.5688L50.0972 50.5566L50.1337 50.5607L50.1703 50.5634L50.318 48.5688ZM51.5738 48.82L52.1583 46.9073L52.1538 46.906L51.5738 48.82ZM53.5203 50.5154L51.6905 51.3226L51.6905 51.3226L53.5203 50.5154ZM53.5203 60.4572L51.6821 59.6694L51.6725 59.6918L51.6634 59.7144L53.5203 60.4572ZM52.7669 61.5874L54.0622 63.1113L54.0681 63.1062L54.0741 63.1011L52.7669 61.5874ZM51.6157 62.1735L51.016 60.2655L51.008 60.268L51.0001 60.2706L51.6157 62.1735ZM50.2552 62.4456L50.1265 60.4497L50.0842 60.4525L50.0422 60.457L50.2552 62.4456ZM50.1087 60.4363L50.644 62.3633L50.693 62.3497L50.7412 62.3336L50.1087 60.4363ZM50.611 59.9967L52.468 60.7395L52.468 60.7395L50.611 59.9967ZM50.611 51.0177L52.468 50.2749L52.468 50.2749L50.611 51.0177ZM50.1087 50.5991L49.4763 52.4964L49.5245 52.5125L49.5734 52.5261L50.1087 50.5991ZM47.5552 50.5991L48.0905 52.5261L48.1242 52.5168L48.1575 52.5062L47.5552 50.5991ZM47.0529 59.9967L48.9099 59.254L47.0529 59.9967ZM47.5552 60.4363L46.9228 62.3336L46.9839 62.354L47.0462 62.3704L47.5552 60.4363ZM48.811 62.5084V60.5084C48.2335 60.5084 47.8093 60.4883 47.5171 60.457L47.3041 62.4456L47.091 64.4342C47.5802 64.4866 48.1607 64.5084 48.811 64.5084V62.5084ZM47.3041 62.4456L47.5171 60.457C47.2518 60.4285 46.9323 60.3682 46.5536 60.2649L46.0273 62.1944L45.5011 64.1239C46.0432 64.2718 46.5749 64.3789 47.091 64.4342L47.3041 62.4456ZM46.0273 62.1944L46.5917 60.2757C46.3151 60.1943 46.2047 60.113 46.1777 60.0899L44.8762 61.6084L43.5746 63.1269C44.1337 63.6061 44.7907 63.9154 45.463 64.1132L46.0273 62.1944ZM44.8762 61.6084L46.218 60.1253C46.2127 60.1206 46.1186 60.0304 46.0006 59.7354L44.1436 60.4781L42.2866 61.2209C42.5593 61.9026 42.9535 62.566 43.5343 63.0914L44.8762 61.6084ZM44.1436 60.4781L45.9734 59.6709C45.904 59.5134 45.8296 59.2314 45.8296 58.7619H43.8296H41.8296C41.8296 59.6319 41.9646 60.4941 42.3138 61.2854L44.1436 60.4781ZM43.8296 58.7619H45.8296V52.2526H43.8296H41.8296V58.7619H43.8296ZM43.8296 52.2526H45.8296C45.8296 51.8107 45.8971 51.5063 45.9796 51.3L44.1227 50.5572L42.2657 49.8144C41.9576 50.5848 41.8296 51.4107 41.8296 52.2526H43.8296ZM44.1227 50.5572L45.9796 51.3C46.0917 51.0198 46.1905 50.9141 46.2215 50.8859L44.8762 49.406L43.5308 47.9262C42.9479 48.4561 42.5443 49.1179 42.2657 49.8144L44.1227 50.5572ZM44.8762 49.406L46.2097 50.8965C46.1951 50.9096 46.27 50.8382 46.5445 50.755L45.9645 48.8409L45.3845 46.9269C44.7381 47.1228 44.0874 47.4281 43.5426 47.9156L44.8762 49.406ZM45.9645 48.8409L46.597 50.7383C46.9184 50.6312 47.2276 50.5669 47.527 50.5397L47.3459 48.5479L47.1649 46.5561C46.5434 46.6126 45.9316 46.7437 45.3321 46.9436L45.9645 48.8409ZM47.3459 48.5479L47.451 50.5451C47.9526 50.5187 48.4054 50.506 48.811 50.506V48.506V46.506C48.3237 46.506 47.7997 46.5213 47.2408 46.5507L47.3459 48.5479ZM48.811 48.506V50.506C49.4062 50.506 49.8256 50.5264 50.0972 50.5566L50.318 48.5688L50.5389 46.5811C50.057 46.5275 49.4717 46.506 48.811 46.506V48.506ZM50.318 48.5688L50.1703 50.5634C50.3669 50.5779 50.6378 50.6262 50.9938 50.7341L51.5738 48.82L52.1538 46.906C51.5889 46.7348 51.0226 46.6156 50.4658 46.5743L50.318 48.5688ZM51.5738 48.82L50.9894 50.7327C51.3176 50.833 51.4426 50.9261 51.4653 50.9455L52.7669 49.427L54.0684 47.9085C53.5051 47.4256 52.8347 47.114 52.1583 46.9073L51.5738 48.82ZM52.7669 49.427L51.4653 50.9455C51.4647 50.945 51.4889 50.9656 51.5306 51.0259C51.5728 51.0868 51.6284 51.1819 51.6905 51.3226L53.5203 50.5154L55.3502 49.7081C55.0596 49.0494 54.6525 48.4091 54.0684 47.9085L52.7669 49.427ZM53.5203 50.5154L51.6905 51.3226C51.7579 51.4755 51.8343 51.7625 51.8343 52.2526H53.8343H55.8343C55.8343 51.3752 55.7014 50.5041 55.3502 49.7081L53.5203 50.5154ZM53.8343 52.2526H51.8343V58.7619H53.8343H55.8343V52.2526H53.8343ZM53.8343 58.7619H51.8343C51.8343 59.188 51.7649 59.4761 51.6821 59.6694L53.5203 60.4572L55.3586 61.2451C55.6944 60.4616 55.8343 59.6195 55.8343 58.7619H53.8343ZM53.5203 60.4572L51.6634 59.7144C51.5528 59.9909 51.4632 60.0707 51.4596 60.0738L52.7669 61.5874L54.0741 63.1011C54.6845 62.5739 55.0972 61.9003 55.3773 61.2L53.5203 60.4572ZM52.7669 61.5874L51.4716 60.0636C51.4602 60.0732 51.3436 60.1625 51.016 60.2655L51.6157 62.1735L52.2153 64.0815C52.8645 63.8775 53.5153 63.5761 54.0622 63.1113L52.7669 61.5874ZM51.6157 62.1735L51.0001 60.2706C50.6603 60.3805 50.3709 60.434 50.1265 60.4497L50.2552 62.4456L50.384 64.4414C51.0047 64.4014 51.6222 64.2734 52.2313 64.0764L51.6157 62.1735ZM50.2552 62.4456L50.0422 60.457C49.7461 60.4887 49.3409 60.5084 48.811 60.5084V62.5084V64.5084C49.4254 64.5084 49.9829 64.4862 50.4683 64.4342L50.2552 62.4456ZM48.8529 60.5409V62.5409C49.452 62.5409 50.1089 62.5119 50.644 62.3633L50.1087 60.4363L49.5734 58.5092C49.6188 58.4966 49.5905 58.5092 49.4403 58.5217C49.303 58.5331 49.1103 58.5409 48.8529 58.5409V60.5409ZM50.1087 60.4363L50.7412 62.3336C51.4426 62.0998 52.1268 61.5925 52.468 60.7395L50.611 59.9967L48.7541 59.254C48.8279 59.0694 48.9485 58.896 49.1057 58.7585C49.256 58.6269 49.3969 58.5654 49.4763 58.5389L50.1087 60.4363ZM50.611 59.9967L52.468 60.7395C52.7029 60.1523 52.7366 59.4814 52.7366 58.9712H50.7366H48.7366C48.7366 59.155 48.7282 59.2737 48.7201 59.3403C48.7162 59.3721 48.7137 59.3806 48.716 59.3706C48.7179 59.3621 48.728 59.3192 48.7541 59.254L50.611 59.9967ZM50.7366 58.9712H52.7366V52.0433H50.7366H48.7366V58.9712H50.7366ZM50.7366 52.0433H52.7366C52.7366 51.533 52.7029 50.8622 52.468 50.2749L50.611 51.0177L48.7541 51.7605C48.728 51.6952 48.7179 51.6524 48.716 51.6438C48.7137 51.6338 48.7162 51.6423 48.7201 51.6741C48.7282 51.7408 48.7366 51.8594 48.7366 52.0433H50.7366ZM50.611 51.0177L52.468 50.2749C52.1144 49.3908 51.3898 48.8792 50.644 48.672L50.1087 50.5991L49.5734 52.5261C49.4785 52.4997 49.3149 52.4363 49.1423 52.2925C48.962 52.1422 48.8312 51.9532 48.7541 51.7605L50.611 51.0177ZM50.1087 50.5991L50.7412 48.7017C50.1589 48.5076 49.4575 48.4735 48.8529 48.4735V50.4735V52.4735C49.1031 52.4735 49.2842 52.4826 49.4074 52.495C49.5446 52.5087 49.5485 52.5205 49.4763 52.4964L50.1087 50.5991ZM48.8529 50.4735V48.4735C48.2288 48.4735 47.5315 48.5092 46.953 48.6919L47.5552 50.5991L48.1575 52.5062C48.1043 52.523 48.1256 52.5097 48.2711 52.4956C48.4045 52.4827 48.595 52.4735 48.8529 52.4735V50.4735ZM47.5552 50.5991L47.0199 48.672C46.2742 48.8792 45.5496 49.3908 45.196 50.2749L47.0529 51.0177L48.9099 51.7605C48.8328 51.9532 48.702 52.1422 48.5216 52.2925C48.3491 52.4363 48.1855 52.4997 48.0905 52.5261L47.5552 50.5991ZM47.0529 51.0177L45.196 50.2749C44.961 50.8622 44.9273 51.533 44.9273 52.0433H46.9273H48.9273C48.9273 51.8594 48.9357 51.7408 48.9439 51.6741C48.9478 51.6423 48.9503 51.6338 48.948 51.6438C48.946 51.6524 48.936 51.6952 48.9099 51.7605L47.0529 51.0177ZM46.9273 52.0433H44.9273V58.9712H46.9273H48.9273V52.0433H46.9273ZM46.9273 58.9712H44.9273C44.9273 59.4814 44.961 60.1523 45.196 60.7395L47.0529 59.9967L48.9099 59.254C48.936 59.3192 48.946 59.3621 48.948 59.3706C48.9503 59.3806 48.9478 59.3721 48.9439 59.3403C48.9357 59.2737 48.9273 59.155 48.9273 58.9712H46.9273ZM47.0529 59.9967L45.196 60.7395C45.5371 61.5925 46.2213 62.0998 46.9228 62.3336L47.5552 60.4363L48.1877 58.5389C48.2671 58.5654 48.408 58.6269 48.5583 58.7585C48.7154 58.896 48.8361 59.0695 48.9099 59.254L47.0529 59.9967ZM47.5552 60.4363L47.0462 62.3704C47.5794 62.5107 48.2348 62.5409 48.8529 62.5409V60.5409V58.5409C48.5879 58.5409 48.3862 58.5331 48.2393 58.5212C48.0812 58.5085 48.0367 58.4949 48.0642 58.5021L47.5552 60.4363ZM60.3871 51.9177H62.3871V48.5242L59.4183 50.168L60.3871 51.9177ZM56.6824 53.9688H54.6824V57.3623L57.6512 55.7186L56.6824 53.9688ZM56.6824 51.3944L55.6232 49.698L54.6824 50.2854V51.3944H56.6824ZM60.9731 48.7154V46.7154H60.4L59.9139 47.0189L60.9731 48.7154ZM63.4429 48.7154H65.4429V46.7154H63.4429V48.7154ZM63.4429 62.2991V64.2991H65.4429V62.2991H63.4429ZM60.3871 62.2991H58.3871V64.2991H60.3871V62.2991ZM60.3871 51.9177L59.4183 50.168L55.7137 52.2191L56.6824 53.9688L57.6512 55.7186L61.3559 53.6674L60.3871 51.9177ZM56.6824 53.9688H58.6824V51.3944H56.6824H54.6824V53.9688H56.6824ZM56.6824 51.3944L57.7417 53.0909L62.0324 50.4118L60.9731 48.7154L59.9139 47.0189L55.6232 49.698L56.6824 51.3944ZM60.9731 48.7154V50.7154H63.4429V48.7154V46.7154H60.9731V48.7154ZM63.4429 48.7154H61.4429V62.2991H63.4429H65.4429V48.7154H63.4429ZM63.4429 62.2991V60.2991H60.3871V62.2991V64.2991H63.4429V62.2991ZM60.3871 62.2991H62.3871V51.9177H60.3871H58.3871V62.2991H60.3871Z" fill="white" mask="url(#path-12-outside-1_1075_4463)"/>
</g>
<g filter="url(#filter3_d_1075_4463)">
<rect x="44.4082" y="6.36621" width="24" height="24" rx="1.5" transform="rotate(6.0008 44.4082 6.36621)" fill="white"/>
</g>
<path d="M61.9764 16.968C61.9343 16.7687 61.8231 16.627 61.6429 16.5431L56.5928 13.0234C56.357 12.8686 56.0453 12.8358 55.7825 12.9382L50.0486 15.3243C49.7857 15.4266 49.5649 15.7283 49.5312 16.0496L48.9232 21.8336C48.8894 22.1549 49.0426 22.4959 49.2784 22.6506L54.3285 26.1704C54.5088 26.2543 54.689 26.3382 54.8828 26.2936C54.9451 26.3002 55.0765 26.249 55.1389 26.2555L60.8104 23.8628C61.1356 23.7671 61.3564 23.4654 61.3902 23.1441L61.9982 17.3602C62.0117 17.2316 61.9629 17.0965 61.9764 16.968ZM55.5853 19.61L55.0111 25.0725L54.8729 25.188L50.0587 21.823L50.6397 16.2961L56.0484 14.0057L60.8626 17.3707L55.5853 19.61Z" fill="#4FC8FF"/>
<rect x="45.7432" y="8.01465" width="2.25" height="2.25" rx="0.75" transform="rotate(6.0008 45.7432 8.01465)" fill="#4FC8FF"/>
<rect x="64.3905" y="9.97485" width="2.25" height="2.25" rx="0.75" transform="rotate(6.0008 64.3905 9.97485)" fill="#4FC8FF"/>
<rect x="62.4303" y="28.6221" width="2.25" height="2.25" rx="0.75" transform="rotate(6.0008 62.4303 28.6221)" fill="#4FC8FF"/>
<rect x="43.7831" y="26.6619" width="2.25" height="2.25" rx="0.75" transform="rotate(6.0008 43.7831 26.6619)" fill="#4FC8FF"/>
<rect x="64.8228" y="13.0369" width="0.75" height="15" rx="0.375" transform="rotate(6.0008 64.8228 13.0369)" fill="#4FC8FF"/>
<rect x="46.6097" y="28.4673" width="0.749999" height="15" rx="0.375" transform="rotate(-83.9992 46.6097 28.4673)" fill="#4FC8FF"/>
<rect x="48.5699" y="9.82007" width="0.749999" height="15" rx="0.375" transform="rotate(-83.9992 48.5699 9.82007)" fill="#4FC8FF"/>
<rect x="46.1755" y="11.0767" width="0.75" height="15" rx="0.375" transform="rotate(6.0008 46.1755 11.0767)" fill="#4FC8FF"/>
<path d="M26.0003 35.8208C25.8336 37.4875 33.6653 43.8208 40.5005 43.8208C47.5001 43.8208 54.0001 42.3208 58.0005 31.3208L55.5005 32.8208" stroke="#4FC8FF" stroke-dasharray="2 1"/>
<defs>
<filter id="filter0_d_1075_4463" x="1.67212" y="5.2616" width="59.0669" height="64.5774" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.43243"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.193025 0 0 0 0 0.482562 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1075_4463"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1075_4463" result="shape"/>
</filter>
<filter id="filter1_d_1075_4463" x="14.2268" y="4.96143" width="30.9509" height="16.2563" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.290706 0 0 0 0 0.792835 0 0 0 0.36 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1075_4463"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1075_4463" result="shape"/>
</filter>
<filter id="filter2_d_1075_4463" x="36.8296" y="44.5061" width="33.6133" height="28.0022" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1075_4463"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1075_4463" result="shape"/>
</filter>
<filter id="filter3_d_1075_4463" x="38.0476" y="3.51465" width="34.0806" height="34.0806" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1075_4463"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1075_4463" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1075_4463" x1="10.4999" y1="-0.445068" x2="49.4999" y2="61.5549" gradientUnits="userSpaceOnUse">
<stop stop-color="#359DFF"/>
<stop offset="1" stop-color="#46F9FF"/>
</linearGradient>
<linearGradient id="paint1_linear_1075_4463" x1="9.16685" y1="3.72937" x2="45.2098" y2="7.9105" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.8"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1075_4463" x1="30.0991" y1="17.8572" x2="30.0991" y2="61.2" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E9FCFF"/>
</linearGradient>
<linearGradient id="paint3_linear_1075_4463" x1="29.463" y1="9.50317" x2="29.463" y2="16.5032" gradientUnits="userSpaceOnUse">
<stop stop-color="#87CFFF"/>
<stop offset="0.475" stop-color="#D5EBFF"/>
<stop offset="0.754808" stop-color="#96C4FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
