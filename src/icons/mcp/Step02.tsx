import type { SVGProps } from "react";
const SvgStep02 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 72 74"
        {...props}
    >
        <g filter="url(#step02_svg__a)">
            <path
                fill="url(#step02_svg__b)"
                fillOpacity={0.9}
                d="M50.038 8.225a2.915 2.915 0 0 1 3.135 2.677l3.616 45.943a4 4 0 0 1-3.674 4.302l-38.88 3.06a4 4 0 0 1-4.301-3.674L6.318 14.59a2.915 2.915 0 0 1 2.677-3.135zm-37.08 8.855a2.5 2.5 0 1 0 .393 4.985 2.5 2.5 0 0 0-.393-4.985"
                shapeRendering="crispEdges"
            />
        </g>
        <path
            fill="url(#step02_svg__c)"
            d="M12.692 34.825 50.856 32l1.752 20.936a2.965 2.965 0 0 1-2.742 3.211l-32.18 2.382a3.035 3.035 0 0 1-3.242-2.768z"
        />
        <path
            stroke="#6DA8FE"
            strokeLinecap="round"
            strokeOpacity={0.3}
            strokeWidth={1.5}
            d="M19.101 41.396c4 .166 11.3-.5 8.5-2.5-3.5-2.5-10-1.5-4 1.5s11.5 1 13.5 0 0-4.5-1-2c-.594 1.485 5 1.666 7.5 1.5M19.601 49.396c1.5.333 4.9.6 6.5-1 2-2-3-2.5-3-1s8.5 3.5 10.5 2-2.5-5-2.5-3 2.5 3 5 2.5 4.5-2.5 6.5-2c1.6.4 4 .833 5 1"
        />
        <path
            fill="#fff"
            d="m50.563 30.32-38.164 2.825-.417-4.986a2.965 2.965 0 0 1 2.742-3.21l32.18-2.382a3.035 3.035 0 0 1 3.241 2.768zM49.057 16.212a3.03 3.03 0 0 1-2.8 3.28l-25.31 1.872a3.1 3.1 0 0 1-3.31-2.826 3.03 3.03 0 0 1 2.8-3.28l25.31-1.872a3.1 3.1 0 0 1 3.31 2.826"
        />
        <path
            fill="#6DA8FE"
            fillOpacity={0.3}
            d="m20.69 16.916.663-.044.591.753.482-.824.663-.044-.807 1.266 1.093 1.305-.668.045-.707-.91-.585.996-.669.044.912-1.439zm2.68-.178.663-.044.59.752.482-.823.664-.045-.808 1.267 1.094 1.305-.668.045-.707-.91-.585.995-.67.045.912-1.439zm2.679-.178.663-.044.591.752.482-.824.663-.044-.807 1.267 1.094 1.305-.669.044-.707-.91-.585.996-.668.045.911-1.439zm2.68-.178.663-.045.591.753.482-.824.663-.044-.808 1.266 1.094 1.306-.668.044-.707-.91-.585.996-.669.045.912-1.439zm2.679-.179.664-.044.59.753.482-.824.664-.044L33 17.31l1.094 1.306-.669.044-.706-.91-.586.996-.668.044.911-1.438zm2.68-.178.663-.044.591.753.482-.824.663-.044-.807 1.266 1.094 1.305-.669.045-.707-.91-.585.996-.669.044.912-1.438zm2.68-.178.663-.044.591.752.482-.823.663-.044-.808 1.266 1.094 1.305-.668.045-.707-.91-.585.996-.669.044.912-1.439zm2.679-.178.663-.044.592.752.481-.824.664-.044-.808 1.267 1.094 1.305-.669.044-.707-.91-.585.996-.668.045.911-1.439z"
        />
        <path
            stroke="url(#step02_svg__d)"
            strokeLinecap="round"
            strokeWidth={1.5}
            d="M6.79 22.737c-1.684 1.646-2.468 3.445-1.78 4.356.831 1.102 3.501.489 5.963-1.37 2.46-1.857 3.782-4.257 2.95-5.359-.336-.444-.97-.61-1.77-.527"
        />
        <g filter="url(#step02_svg__e)">
            <mask
                id="step02_svg__f"
                width={26}
                height={19}
                x={40.863}
                y={47.14}
                fill="#000"
                maskUnits="userSpaceOnUse"
            >
                <path fill="#fff" d="M40.863 47.14h26v19h-26z" />
                <path d="M47.912 63.349q-.92 0-1.507-.063a8 8 0 0 1-1.277-.251q-.711-.21-1.15-.586-.44-.398-.733-1.13-.314-.712-.314-1.717v-6.509q0-.963.293-1.695.292-.733.753-1.151.398-.357 1.089-.566.69-.23 1.381-.292.795-.042 1.465-.042.942 0 1.507.062.565.042 1.256.252.753.23 1.193.607.44.376.753 1.088.315.711.314 1.737v6.51q0 .962-.314 1.695-.292.732-.753 1.13-.42.356-1.151.586a5.6 5.6 0 0 1-1.36.272q-.587.063-1.445.063m.042-1.967q.88 0 1.256-.105.376-.126.502-.44.126-.313.126-1.025v-6.928q0-.712-.126-1.026t-.502-.418q-.377-.126-1.256-.126-.9 0-1.298.126-.376.105-.502.418-.126.314-.126 1.026v6.928q0 .711.126 1.025.126.315.502.44.398.105 1.298.105m7.264-1.34q0-1.131.628-1.968a4 4 0 0 1 1.633-1.276q.795-.356 1.863-.712 1.067-.355 1.653-.753t.586-.942v-1.737q0-.65-.523-.859-.566-.23-1.758-.23-1.444 0-3.81.524v-2.303a21.2 21.2 0 0 1 4.375-.44q2.469 0 3.663.754 1.214.754 1.214 2.512v1.883q0 1.068-.65 1.863-.627.775-1.632 1.172-.942.398-1.925.712-1.068.377-1.696.837-.607.46-.607 1.089v.69h6.53v2.282h-9.544z" />
            </mask>
            <path
                fill="#6DA8FE"
                d="M47.912 63.349q-.92 0-1.507-.063a8 8 0 0 1-1.277-.251q-.711-.21-1.15-.586-.44-.398-.733-1.13-.314-.712-.314-1.717v-6.509q0-.963.293-1.695.292-.733.753-1.151.398-.357 1.089-.566.69-.23 1.381-.292.795-.042 1.465-.042.942 0 1.507.062.565.042 1.256.252.753.23 1.193.607.44.376.753 1.088.315.711.314 1.737v6.51q0 .962-.314 1.695-.292.732-.753 1.13-.42.356-1.151.586a5.6 5.6 0 0 1-1.36.272q-.587.063-1.445.063m.042-1.967q.88 0 1.256-.105.376-.126.502-.44.126-.313.126-1.025v-6.928q0-.712-.126-1.026t-.502-.418q-.377-.126-1.256-.126-.9 0-1.298.126-.376.105-.502.418-.126.314-.126 1.026v6.928q0 .711.126 1.025.126.315.502.44.398.105 1.298.105m7.264-1.34q0-1.131.628-1.968a4 4 0 0 1 1.633-1.276q.795-.356 1.863-.712 1.067-.355 1.653-.753t.586-.942v-1.737q0-.65-.523-.859-.566-.23-1.758-.23-1.444 0-3.81.524v-2.303a21.2 21.2 0 0 1 4.375-.44q2.469 0 3.663.754 1.214.754 1.214 2.512v1.883q0 1.068-.65 1.863-.627.775-1.632 1.172-.942.398-1.925.712-1.068.377-1.696.837-.607.46-.607 1.089v.69h6.53v2.282h-9.544z"
            />
            <path
                fill="#fff"
                d="m46.405 63.286.213-1.989zm-1.277-.251-.564 1.919.02.005.018.005zm-1.15-.586-1.343 1.483.02.018.02.017zm-.733-1.13 1.857-.743-.013-.032-.014-.033zm-.021-9.921-1.857-.743zm.753-1.151-1.333-1.49-.006.004-.006.006zm1.089-.566.58 1.915.026-.008.026-.01zm1.381-.292-.105-1.998-.038.002-.038.004zm2.972.02-.22 1.988.036.004.036.003zm1.256.252.584-1.913-.004-.002zm1.946 1.695-1.83.807zm0 9.942-1.838-.788-.01.022-.009.023zm-.753 1.13 1.295 1.524.006-.005.006-.005zm-1.151.586-.6-1.908-.008.003-.008.002zm-1.36.272-.13-1.996-.042.003-.042.005zm-.147-2.01.535 1.928.049-.014.048-.016zm.502-.439 1.857.743zm0-8.979 1.857-.742zm-.502-.418-.633 1.897.049.016.049.014zm-2.554 0 .536 1.927.033-.01.034-.01zm-.502 9.397 1.857-.743zm.502.44-.632 1.897.061.02.062.017zm1.256 2.072v-2c-.577 0-1.002-.02-1.294-.051l-.213 1.988-.213 1.989c.49.052 1.07.074 1.72.074zm-1.507-.063.213-1.989a6 6 0 0 1-.963-.191l-.527 1.929-.526 1.93c.542.147 1.074.254 1.59.31zm-1.277-.251.565-1.919c-.277-.081-.387-.163-.414-.186l-1.302 1.519-1.301 1.519c.559.479 1.216.788 1.888.986zm-1.15-.586 1.341-1.483c-.005-.005-.1-.095-.217-.39l-1.857.743-1.857.742c.272.682.667 1.346 1.247 1.871zm-.733-1.13 1.83-.808c-.07-.157-.144-.44-.144-.909h-4c0 .87.135 1.733.484 2.524zm-.314-1.717h2v-6.509h-4v6.51zm0-6.509h2c0-.442.067-.746.15-.952l-1.857-.743-1.857-.743c-.308.77-.436 1.596-.436 2.438zm.293-1.695 1.857.743c.112-.28.21-.386.242-.414l-1.346-1.48-1.345-1.48c-.583.53-.987 1.191-1.265 1.888zm.753-1.151 1.334 1.49c-.015.013.06-.058.335-.141l-.58-1.914-.58-1.915c-.647.196-1.297.502-1.842.99zm1.089-.566.632 1.898q.481-.16.93-.199l-.181-1.992-.181-1.991a8 8 0 0 0-1.833.387zm1.381-.292.105 1.997q.753-.04 1.36-.04v-4q-.731 0-1.57.045zm1.465-.042v2c.595 0 1.015.02 1.286.05l.221-1.988.221-1.987a16 16 0 0 0-1.728-.075zm1.507.062-.148 1.995c.197.014.468.063.824.17l.58-1.913.58-1.915a8 8 0 0 0-1.688-.331zm1.256.252-.584 1.912c.328.1.453.194.475.213l1.302-1.518 1.301-1.519c-.563-.483-1.233-.794-1.91-1.001zm1.193.607-1.302 1.518s.024.02.066.08c.042.061.098.156.16.297l1.83-.807 1.83-.807c-.291-.659-.698-1.3-1.282-1.8zm.753 1.088-1.83.807c.068.153.144.44.144.93h4c0-.877-.133-1.748-.484-2.544zm.314 1.737h-2v6.51h4v-6.51zm0 6.51h-2c0 .426-.069.714-.152.907l1.838.788 1.839.788c.335-.784.475-1.626.475-2.484zm-.314 1.695-1.857-.743c-.11.276-.2.356-.203.36l1.307 1.513 1.307 1.514c.61-.528 1.023-1.201 1.303-1.901zm-.753 1.13-1.295-1.524c-.012.01-.128.1-.456.202l.6 1.908.6 1.908c.649-.204 1.3-.505 1.846-.97zm-1.151.586-.616-1.903c-.34.11-.629.164-.873.18l.128 1.995.13 1.996a7.6 7.6 0 0 0 1.846-.365zm-1.36.272-.214-1.989c-.296.032-.701.052-1.23.052v4c.613 0 1.171-.022 1.656-.074zm-1.403-1.904v2c.6 0 1.256-.03 1.791-.178l-.535-1.927-.535-1.927c.045-.013.017 0-.134.012-.137.012-.33.02-.587.02zm1.256-.105.632 1.897c.702-.234 1.386-.741 1.727-1.594l-1.857-.743-1.857-.743a1.3 1.3 0 0 1 .352-.495c.15-.131.29-.193.37-.22zm.502-.44 1.857.743c.235-.587.269-1.258.269-1.768h-4c0 .184-.009.302-.017.369l-.004.03a1 1 0 0 1 .038-.117zm.126-1.025h2v-6.928h-4v6.928zm0-6.928h2c0-.51-.034-1.181-.269-1.769l-1.857.743-1.857.743a1 1 0 0 1-.038-.117l.004.03c.008.067.017.186.017.37zm-.126-1.026 1.857-.742c-.354-.885-1.078-1.396-1.824-1.603l-.535 1.927-.535 1.927a1.2 1.2 0 0 1-.432-.234c-.18-.15-.31-.34-.388-.532zm-.502-.418.632-1.898c-.582-.194-1.283-.228-1.888-.228v4c.25 0 .431.01.554.022.138.013.142.025.07.001zm-1.256-.126v-2c-.624 0-1.321.036-1.9.218l.602 1.908.603 1.907c-.054.017-.032.003.113-.01.134-.014.324-.023.582-.023zm-1.298.126-.535-1.927c-.746.207-1.47.718-1.824 1.602l1.857.743 1.857.743a1.35 1.35 0 0 1-.388.532 1.2 1.2 0 0 1-.431.234zm-.502.418-1.857-.742c-.235.587-.269 1.258-.269 1.768h4c0-.184.009-.303.017-.37l.004-.03a1 1 0 0 1-.038.117zm-.126 1.026h-2v6.928h4v-6.928zm0 6.928h-2c0 .51.034 1.18.269 1.768l1.857-.743 1.857-.743a1 1 0 0 1 .038.117l-.004-.03a3 3 0 0 1-.017-.37zm.126 1.025-1.857.743c.341.853 1.025 1.36 1.727 1.594l.632-1.897.633-1.898c.08.027.22.089.37.22.157.138.278.311.352.495zm.502.44-.509 1.934c.534.14 1.189.17 1.807.17v-4c-.265 0-.467-.007-.614-.02-.158-.012-.202-.025-.175-.018zm9.19-3.203-1.6-1.2zm1.633-1.276.802 1.832.007-.003.008-.004zm1.863-.712.632 1.898zm1.653-.753-1.123-1.655zm.063-3.538-.755 1.853.012.004zm-5.568.294h-2v2.49l2.432-.538zm0-2.303-.41-1.957-1.59.334v1.623zm8.038.314-1.068 1.691.006.004.007.004zm.565 6.258-1.55-1.264-.004.005zM62.46 57.53l-.736-1.86-.02.01-.022.008zm-1.925.712-.608-1.905-.03.009-.028.01zm-1.696.837-1.182-1.613-.013.01-.013.01zm-.607 1.78h-2v2h2zm6.53 0h2v-2h-2zm0 2.28v2h2v-2zm-9.544 0h-2v2h2zm0-3.097h2c0-.358.09-.584.228-.767l-1.6-1.2-1.6-1.2c-.7.932-1.028 2.017-1.028 3.167zm.628-1.968 1.6 1.2c.202-.268.465-.482.835-.644l-.802-1.832-.802-1.833a6 6 0 0 0-2.43 1.91zm1.633-1.276.817 1.825a19 19 0 0 1 1.678-.64l-.632-1.897-.633-1.897c-.757.252-1.442.513-2.047.783zm1.863-.712.632 1.898c.796-.266 1.54-.586 2.144-.996l-1.123-1.655-1.123-1.655c-.177.12-.536.302-1.163.51zm1.653-.753 1.123 1.655c.755-.513 1.463-1.376 1.463-2.597h-4c0-.2.06-.395.156-.55.084-.136.16-.18.135-.163zm.586-.942h2v-1.737h-4v1.737zm0-1.737h2c0-.479-.096-1.045-.423-1.58a2.78 2.78 0 0 0-1.357-1.136l-.743 1.857-.743 1.857a1.22 1.22 0 0 1-.572-.496 1 1 0 0 1-.162-.502zm-.523-.859.755-1.852c-.75-.305-1.659-.378-2.513-.378v4c.338 0 .593.017.776.04.193.026.252.053.227.043zm-1.758-.23v-2c-1.173 0-2.604.209-4.241.57l.431 1.954.432 1.952c1.517-.335 2.625-.476 3.378-.476zm-3.81.524h2v-2.303h-4v2.303zm0-2.303.411 1.957a19.2 19.2 0 0 1 3.964-.396v-4q-2.476-.001-4.785.482zm4.375-.44v2c1.517 0 2.279.245 2.595.445l1.068-1.69 1.068-1.692c-1.275-.805-2.955-1.062-4.731-1.062zm3.663.754-1.055 1.7c.08.05.269.147.269.812h4c0-1.68-.621-3.256-2.16-4.211zm1.214 2.512h-2v1.883h4v-1.883zm0 1.883h-2c0 .271-.07.44-.199.599l1.55 1.264 1.55 1.265a4.86 4.86 0 0 0 1.099-3.127zm-.65 1.863-1.553-1.26c-.184.228-.437.423-.815.573l.736 1.86.736 1.859a5.9 5.9 0 0 0 2.45-1.772zM62.46 57.53l-.778-1.842q-.857.361-1.755.649l.608 1.905.608 1.905a25 25 0 0 0 2.095-.774zm-1.925.712-.666-1.886c-.82.29-1.582.648-2.212 1.11l1.182 1.613 1.183 1.613c.207-.152.576-.351 1.178-.564zm-1.696.837-1.208-1.593c-.785.595-1.399 1.502-1.399 2.682h4c0 .129-.035.269-.101.388-.06.107-.113.14-.083.117zm-.607 1.089h-2v.69h4v-.69zm0 .69v2h6.53v-4h-6.53zm6.53 0h-2v2.282h4v-2.282zm0 2.282v-2h-9.544v4h9.545zm-9.544 0h2v-3.098h-4v3.098z"
                mask="url(#step02_svg__f)"
            />
        </g>
        <g filter="url(#step02_svg__g)">
            <rect
                width={24}
                height={24}
                x={43.61}
                y={14.896}
                fill="#fff"
                rx={12}
                transform="rotate(6 43.61 14.896)"
            />
        </g>
        <path
            fill="#6DA8FE"
            d="M52.325 32.892a.55.55 0 0 1-.236.138l-3.934 1.132a.272.272 0 0 1-.336-.336l1.132-3.938a.54.54 0 0 1 .138-.234l8.017-8.015a.54.54 0 0 1 .765 0l2.47 2.469a.544.544 0 0 1 0 .767zm4.205-8.76 1.318 1.318.959-.96-1.32-1.317zm-.864.862-5.47 5.474-.532 1.849 1.85-.532 5.47-5.47-1.318-1.319zm-6.876-3.459c-.064-.182-.165-.182-.227 0l-.351 1.035a.8.8 0 0 1-.446.447l-1.027.35c-.182.06-.185.165 0 .227l1.02.357a.82.82 0 0 1 .446.448l.357 1.034c.061.183.165.182.225 0l.347-1.03a.8.8 0 0 1 .448-.447l1.041-.36c.185-.06.185-.164 0-.224l-1.026-.347a.81.81 0 0 1-.449-.446l-.358-1.042zm10.07 9.19c-.088-.252-.23-.252-.317 0l-.489 1.436a1.13 1.13 0 0 1-.62.62l-1.429.484c-.253.087-.255.228 0 .317l1.42.493c.254.089.533.368.621.621l.494 1.436c.089.254.23.254.316 0l.484-1.43a1.13 1.13 0 0 1 .62-.622l1.45-.494c.255-.087.255-.228 0-.315l-1.428-.482a1.13 1.13 0 0 1-.623-.616l-.499-1.446z"
        />
        <path
            stroke="#6DA8FE"
            strokeDasharray="2 1"
            d="M61.601 16.395c-2.5-1.875-4.667-2.5-5.5-2.5.333-1.833.76-5.291-2-7.5-2.5-2-7.5-1-11-.5"
        />
        <defs>
            <linearGradient
                id="step02_svg__b"
                x1={28.629}
                x2={33.075}
                y1={-7.811}
                y2={48.681}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#5680FE" />
                <stop offset={1} stopColor="#46CEFF" />
            </linearGradient>
            <linearGradient
                id="step02_svg__c"
                x1={31.774}
                x2={40.479}
                y1={33.412}
                y2={55.584}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#E8F1FF" />
            </linearGradient>
            <linearGradient
                id="step02_svg__d"
                x1={13.93}
                x2={13.656}
                y1={23.101}
                y2={19.611}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#6BE1FF" />
                <stop offset={1} stopColor="#4D6BB9" />
            </linearGradient>
            <filter
                id="step02_svg__a"
                width={58.493}
                height={64.003}
                x={2.309}
                y={4.216}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.192157 0 0 0 0 0.482353 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4694"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4694"
                    result="shape"
                />
            </filter>
            <filter
                id="step02_svg__e"
                width={35.832}
                height={28.002}
                x={35.931}
                y={45.347}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={3} />
                <feGaussianBlur stdDeviation={2.5} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.15 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4694"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4694"
                    result="shape"
                />
            </filter>
            <filter
                id="step02_svg__g"
                width={32.002}
                height={32.002}
                x={38.289}
                y={13.083}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={1} />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.2 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4694"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4694"
                    result="shape"
                />
            </filter>
        </defs>
    </svg>
);
export default SvgStep02;
