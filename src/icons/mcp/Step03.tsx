import type { SVGProps } from "react";
const SvgStep03 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 75 75"
        {...props}
    >
        <circle
            cx={42.127}
            cy={7.929}
            r={3}
            stroke="#6C7AFF"
            strokeDasharray="2 1"
        />
        <g filter="url(#step03_svg__a)">
            <path
                fill="url(#step03_svg__b)"
                d="M39.56 6.78 8.798 9.201a4.57 4.57 0 0 0-4.199 4.916l2.96 37.598a4.57 4.57 0 0 0 4.915 4.199l30.762-2.421z"
                opacity={0.8}
            />
            <path
                fill="#fff"
                d="M10.363 17.246a3 3 0 0 1 2.755-3.226l19.938-1.57a3 3 0 0 1 3.227 2.756l.313 3.988a3 3 0 0 1-2.755 3.226l-19.938 1.57a3 3 0 0 1-3.226-2.756z"
            />
            <path
                fill="#fff"
                fillOpacity={0.9}
                d="M11.383 30.206a3 3 0 0 1 2.755-3.226l19.939-1.569a3 3 0 0 1 3.226 2.755l.314 3.988a3 3 0 0 1-2.756 3.226l-19.938 1.57a3 3 0 0 1-3.226-2.756z"
            />
            <path
                fill="#fff"
                fillOpacity={0.7}
                d="M12.403 43.166a3 3 0 0 1 2.755-3.226l19.938-1.57a3 3 0 0 1 3.227 2.756l.392 4.985a3 3 0 0 1-2.756 3.226l-19.938 1.569a3 3 0 0 1-3.226-2.755z"
            />
            <path
                fill="url(#step03_svg__c)"
                d="m20.703 15.704 35.077-2.761a4.57 4.57 0 0 1 4.916 4.199l2.959 37.598a4.57 4.57 0 0 1-4.199 4.916l-35.076 2.76z"
            />
            <path
                fill="#fff"
                d="M25.62 23.58a3 3 0 0 1 2.755-3.226l24.896-1.96a3 3 0 0 1 3.227 2.756l.719 9.14a3 3 0 0 1-2.755 3.226l-24.897 1.96a3 3 0 0 1-3.226-2.756z"
            />
            <path
                fill="#fff"
                fillOpacity={0.9}
                d="M27.23 41.36a1.754 1.754 0 0 1 1.61-1.885l27.172-2.139a1.754 1.754 0 0 1 .276 3.497L29.115 42.97a1.753 1.753 0 0 1-1.886-1.61"
            />
            <path
                fill="#fff"
                fillOpacity={0.7}
                d="M27.98 47.38a1.754 1.754 0 0 1 1.611-1.886l26.897-2.116a1.754 1.754 0 0 1 .275 3.496l-26.896 2.117a1.754 1.754 0 0 1-1.886-1.61"
            />
            <path fill="#B072BF" d="M25.018 15.364 40.5 14l-.671-7z" />
        </g>
        <g filter="url(#step03_svg__d)">
            <mask
                id="step03_svg__e"
                width={26}
                height={19}
                x={43.759}
                y={48.572}
                fill="#000"
                maskUnits="userSpaceOnUse"
            >
                <path fill="#fff" d="M43.759 48.572h26v19h-26z" />
                <path d="M50.808 64.78q-.92 0-1.507-.062a8 8 0 0 1-1.277-.251q-.712-.21-1.15-.586-.44-.398-.733-1.13-.315-.712-.314-1.717v-6.509q0-.963.293-1.695t.753-1.151q.398-.356 1.089-.566.69-.229 1.38-.293.797-.041 1.466-.041.942 0 1.507.062.565.042 1.256.252.753.23 1.193.606.44.377.753 1.089.314.711.314 1.737v6.51q0 .962-.314 1.695-.292.732-.753 1.13-.42.356-1.151.586a5.6 5.6 0 0 1-1.36.272q-.587.063-1.445.063m.042-1.967q.88 0 1.256-.104.376-.125.502-.44t.126-1.025v-6.928q0-.712-.126-1.026t-.502-.419q-.378-.125-1.256-.125-.9 0-1.298.125-.376.105-.502.42-.126.312-.126 1.025v6.928q0 .711.126 1.025.126.315.502.44.398.104 1.298.104m11.597 1.968q-1.758 0-4.563-.314v-2.428l.67.126q.397.083.44.104l.711.126q.25.042 1.067.125.629.063 1.277.063 1.215 0 1.675-.293.48-.292.481-1.11v-1.569q0-.984-2.114-.984h-2.47v-2.093h2.47q1.633 0 1.633-1.193v-1.318q0-.44-.168-.67-.167-.252-.607-.356-.418-.105-1.256-.105-1.193 0-3.683.252v-2.177q2.846-.188 4.018-.188 2.554 0 3.684.586 1.15.585 1.151 2.072v2.24q0 .606-.377 1.046-.356.418-.92.565 1.84.649 1.841 2.03v2.219q0 1.737-1.193 2.49-1.192.754-3.767.754" />
            </mask>
            <path
                fill="#7E8BFF"
                d="M50.808 64.78q-.92 0-1.507-.062a8 8 0 0 1-1.277-.251q-.712-.21-1.15-.586-.44-.398-.733-1.13-.315-.712-.314-1.717v-6.509q0-.963.293-1.695t.753-1.151q.398-.356 1.089-.566.69-.229 1.38-.293.797-.041 1.466-.041.942 0 1.507.062.565.042 1.256.252.753.23 1.193.606.44.377.753 1.089.314.711.314 1.737v6.51q0 .962-.314 1.695-.292.732-.753 1.13-.42.356-1.151.586a5.6 5.6 0 0 1-1.36.272q-.587.063-1.445.063m.042-1.967q.88 0 1.256-.104.376-.125.502-.44t.126-1.025v-6.928q0-.712-.126-1.026t-.502-.419q-.378-.125-1.256-.125-.9 0-1.298.125-.376.105-.502.42-.126.312-.126 1.025v6.928q0 .711.126 1.025.126.315.502.44.398.104 1.298.104m11.597 1.968q-1.758 0-4.563-.314v-2.428l.67.126q.397.083.44.104l.711.126q.25.042 1.067.125.629.063 1.277.063 1.215 0 1.675-.293.48-.292.481-1.11v-1.569q0-.984-2.114-.984h-2.47v-2.093h2.47q1.633 0 1.633-1.193v-1.318q0-.44-.168-.67-.167-.252-.607-.356-.418-.105-1.256-.105-1.193 0-3.683.252v-2.177q2.846-.188 4.018-.188 2.554 0 3.684.586 1.15.585 1.151 2.072v2.24q0 .606-.377 1.046-.356.418-.92.565 1.84.649 1.841 2.03v2.219q0 1.737-1.193 2.49-1.192.754-3.767.754"
            />
            <path
                fill="#fff"
                d="m49.301 64.718.213-1.989zm-1.277-.251-.564 1.919.019.005.019.005zm-1.15-.586-1.343 1.483.02.018.02.017zm-.733-1.13 1.857-.743-.014-.033-.014-.032zm-.021-9.921-1.857-.743zm.753-1.151-1.334-1.491-.005.005-.006.006zm1.089-.566.58 1.914.026-.008.026-.008zm1.38-.293-.104-1.997-.038.002-.038.004zm2.973.021-.22 1.988.036.004.036.003zm1.256.252.584-1.913-.004-.002zm1.946 1.695-1.83.807zm0 9.942-1.838-.788-.01.022-.009.023zm-.753 1.13 1.295 1.524.006-.005.006-.005zm-1.151.586-.6-1.908-.008.003-.008.002zm-1.36.272-.13-1.996-.042.003-.042.004zm-.147-2.01.535 1.928.049-.014.048-.016zm.502-.439 1.857.743zm0-8.979 1.857-.743zm-.502-.419-.633 1.898.048.016.05.014zm-2.554 0 .535 1.928.034-.01.033-.01zm-.502 9.398 1.857-.743zm.502.44-.632 1.897.06.02.063.017zm1.256 2.072v-2c-.578 0-1.002-.02-1.294-.052l-.213 1.989-.213 1.989c.49.052 1.07.074 1.72.074zm-1.507-.063.213-1.989a6 6 0 0 1-.964-.192l-.526 1.93-.526 1.93c.542.147 1.074.254 1.59.31zm-1.277-.251.565-1.919c-.277-.081-.387-.163-.414-.186l-1.302 1.519-1.301 1.518c.559.48 1.216.789 1.888.987zm-1.15-.586 1.34-1.483c-.004-.005-.099-.095-.216-.39l-1.857.743-1.857.742c.272.682.666 1.345 1.247 1.87zm-.733-1.13 1.83-.808c-.07-.157-.144-.44-.144-.909h-4c0 .87.135 1.733.484 2.524zm-.314-1.717h2v-6.509h-4v6.51zm0-6.509h2c0-.442.067-.746.15-.953l-1.857-.742-1.857-.743c-.308.77-.436 1.596-.436 2.438zm.293-1.695 1.857.742c.112-.28.21-.385.242-.414l-1.346-1.48-1.345-1.48c-.583.53-.987 1.192-1.265 1.889zm.753-1.151 1.334 1.49c-.015.013.06-.058.334-.142l-.58-1.914-.58-1.914c-.646.196-1.297.502-1.842.989zm1.089-.566.632 1.898q.48-.16.93-.199l-.181-1.992-.181-1.991a8 8 0 0 0-1.833.387zm1.38-.293.106 1.998q.752-.04 1.36-.04v-4q-.731 0-1.57.045zm1.466-.041v2c.595 0 1.014.02 1.286.05l.221-1.988.22-1.988a16 16 0 0 0-1.727-.075zm1.507.062-.148 1.995c.197.014.468.063.824.17l.58-1.913.58-1.915a8 8 0 0 0-1.688-.331zm1.256.252-.585 1.912c.329.1.454.194.476.213l1.302-1.519 1.301-1.518c-.563-.483-1.233-.795-1.91-1.001zm1.193.606-1.302 1.519s.024.02.066.08c.042.061.097.156.16.297l1.83-.807 1.83-.807c-.291-.66-.698-1.3-1.283-1.8zm.753 1.089-1.83.807c.068.153.144.44.144.93h4c0-.877-.133-1.748-.484-2.544zm.314 1.737h-2v6.51h4v-6.51zm0 6.51h-2c0 .425-.07.714-.152.907l1.838.788 1.839.788c.335-.784.475-1.626.475-2.484zm-.314 1.695-1.857-.743c-.11.276-.2.356-.203.36l1.307 1.513 1.307 1.514c.61-.528 1.023-1.201 1.303-1.901zm-.753 1.13-1.296-1.524c-.01.01-.127.099-.455.202l.6 1.908.6 1.908c.648-.204 1.3-.505 1.846-.97zm-1.151.586-.616-1.903c-.34.11-.63.163-.874.18l.13 1.995.128 1.996a7.6 7.6 0 0 0 1.847-.365zm-1.36.272-.214-1.989a12 12 0 0 1-1.231.052v4c.614 0 1.172-.022 1.657-.074zm-1.403-1.905v2c.599 0 1.256-.029 1.791-.177l-.535-1.927-.536-1.927c.046-.013.017 0-.133.012a7 7 0 0 1-.587.02zm1.256-.104.632 1.897c.702-.234 1.386-.741 1.727-1.594l-1.857-.743-1.857-.743c.074-.184.195-.358.352-.495.15-.132.29-.193.37-.22zm.502-.44 1.857.743c.235-.587.269-1.258.269-1.768h-4c0 .184-.009.302-.017.369l-.004.03a1 1 0 0 1 .038-.117zm.126-1.025h2v-6.928h-4v6.928zm0-6.928h2c0-.51-.034-1.181-.27-1.769l-1.856.743-1.857.743c-.026-.065-.036-.108-.038-.117l.004.03c.008.067.017.186.017.37zm-.126-1.026 1.857-.743c-.354-.884-1.078-1.395-1.824-1.602l-.535 1.926-.536 1.928a1.2 1.2 0 0 1-.43-.234c-.181-.15-.312-.34-.389-.532zm-.502-.419.632-1.897c-.582-.194-1.284-.228-1.888-.228v4c.25 0 .431.01.554.021.138.014.141.026.07.002zm-1.256-.125v-2c-.624 0-1.322.036-1.9.218l.602 1.907.602 1.908c-.053.016-.032.003.114-.011a6 6 0 0 1 .582-.022zm-1.298.125-.535-1.927c-.746.208-1.47.72-1.824 1.603l1.857.743 1.857.743a1.35 1.35 0 0 1-.388.532 1.2 1.2 0 0 1-.431.234zm-.502.42-1.857-.744c-.235.588-.269 1.259-.269 1.769h4c0-.184.009-.303.017-.37l.004-.03a1 1 0 0 1-.038.117zm-.126 1.025h-2v6.928h4v-6.928zm0 6.928h-2c0 .51.034 1.18.269 1.768l1.857-.743 1.857-.743a1 1 0 0 1 .038.117l-.004-.03a3 3 0 0 1-.017-.37zm.126 1.025-1.857.743c.341.853 1.025 1.36 1.727 1.594l.632-1.897.633-1.898c.079.027.22.088.37.22.157.137.278.31.352.495zm.502.44-.509 1.934c.533.14 1.189.17 1.807.17v-4q-.396 0-.614-.02c-.158-.012-.202-.026-.175-.018zm8.332 1.758h-2v1.788l1.778.2zm0-2.428.369-1.966-2.369-.444v2.41zm.67.126.412-1.957-.022-.005-.022-.004zm.44.104-.895 1.79.26.13.287.05zm.711.126-.348 1.97.01.001.01.002zm1.067.125-.204 1.99h.005zm2.952-.23-1.04-1.708-.017.01-.017.01zm-4.103-3.663h-2v2h2zm0-2.093v-2h-2v2zm3.935-3.181-1.664 1.11.023.033.024.033zm-.607-.356-.485 1.94.011.003.01.003zm-4.94.147h-2v2.212l2.201-.223zm0-2.177-.131-1.996-1.868.124v1.872zm7.703.398-.92 1.775.006.004.007.003zm.774 5.358-1.518-1.302-.005.006zm-.92.565-.502-1.936-6.345 1.645 6.182 2.177zm.648 6.74 1.068 1.69zm-3.767.753v-2c-1.067 0-2.506-.096-4.34-.302l-.223 1.988-.222 1.987c1.905.214 3.508.327 4.785.327zm-4.563-.314h2v-2.428h-4v2.428zm0-2.428-.368 1.966.67.125.368-1.965.368-1.966-.67-.126zm.67.126-.412 1.957.278.061-.01-.002a1 1 0 0 1-.146-.05 2 2 0 0 1-.165-.073l.894-1.789.895-1.789c-.134-.067-.248-.104-.278-.114a3 3 0 0 0-.284-.079l-.36-.08zm.44.104-.348 1.97.711.125.348-1.97.348-1.969-.712-.125zm.711.126-.329 1.973c.241.04.662.088 1.192.142l.204-1.99.205-1.99a22 22 0 0 1-.943-.108zm1.067.125-.199 1.99q.73.074 1.476.073v-4q-.552 0-1.078-.053zm1.277.063v2c.827 0 1.912-.073 2.748-.605l-1.073-1.688-1.074-1.687c.087-.056.148-.077.155-.079s-.013.005-.075.016a4 4 0 0 1-.68.043zm1.675-.293L64.764 64c1.16-.707 1.441-1.918 1.441-2.818h-4c0 .089-.015.027.045-.11.072-.166.215-.356.434-.49zm.481-1.11h2v-1.569h-4v1.57zm0-1.569h2a2.72 2.72 0 0 0-.506-1.595 3 3 0 0 0-1.179-.956c-.752-.35-1.632-.433-2.429-.433v4c.28 0 .481.017.618.038.143.022.168.042.124.022a1.05 1.05 0 0 1-.386-.342 1.3 1.3 0 0 1-.242-.734zm-2.114-.984v-2h-2.47v4h2.47zm-2.47 0h2v-2.093h-4v2.093zm0-2.093v2h2.47v-4h-2.47zm2.47 0v2c.729 0 1.637-.122 2.405-.683.884-.646 1.228-1.604 1.228-2.51h-4c0-.027.005-.147.078-.308.078-.172.2-.315.333-.412.121-.088.207-.107.197-.104a1.2 1.2 0 0 1-.241.017zm1.633-1.193h2v-1.318h-4v1.318zm0-1.318h2c0-.5-.088-1.21-.55-1.846l-1.618 1.176-1.617 1.176a1.2 1.2 0 0 1-.198-.408c-.02-.084-.017-.123-.017-.098zm-.168-.67 1.664-1.11c-.503-.755-1.262-1.062-1.808-1.192l-.463 1.946-.463 1.946-.012-.003-.033-.012a1 1 0 0 1-.132-.064 1.3 1.3 0 0 1-.417-.402zm-.607-.356.485-1.94c-.524-.131-1.142-.165-1.74-.165v4c.24 0 .426.008.566.02.146.012.205.026.204.025zm-1.256-.105v-2c-.901 0-2.213.093-3.884.262l.2 1.99.201 1.99c1.65-.167 2.794-.242 3.483-.242zm-3.683.252h2v-2.177h-4v2.177zm0-2.177.132 1.995c1.898-.125 3.178-.184 3.886-.184v-4c-.854 0-2.253.068-4.15.193zm4.018-.188v2c1.643 0 2.46.203 2.763.361l.921-1.775.92-1.776c-1.202-.624-2.842-.81-4.604-.81zm3.684.586-.907 1.782c.038.02.047.03.041.025a.1.1 0 0 1-.019-.025c-.01-.018.036.05.036.29h4c0-.75-.146-1.524-.54-2.233a4 4 0 0 0-1.704-1.622zm1.151 2.072h-2v2.24h4v-2.24zm0 2.24h-2a.465.465 0 0 1 .105-.255l1.518 1.3 1.519 1.302a3.55 3.55 0 0 0 .858-2.348zm-.377 1.046-1.523-1.296a.3.3 0 0 1 .07-.058c.028-.018.043-.02.03-.017l.502 1.936.502 1.936a3.75 3.75 0 0 0 1.943-1.206zm-.92.565-.665 1.886c.426.15.555.28.565.29q-.001 0-.014-.02a.3.3 0 0 1-.027-.053c-.017-.047-.018-.077-.018-.073h4c0-.953-.335-1.832-.979-2.542-.605-.667-1.396-1.092-2.198-1.375zm1.841 2.03h-2v2.219h4v-2.219zm0 2.219h-2c0 .654-.185.751-.26.8l1.067 1.69 1.068 1.691c1.515-.956 2.125-2.52 2.125-4.181zm-1.193 2.49-1.068-1.69c-.307.193-1.093.444-2.7.444v4c1.828 0 3.553-.252 4.836-1.063z"
                mask="url(#step03_svg__e)"
            />
        </g>
        <g filter="url(#step03_svg__f)">
            <rect
                width={24}
                height={20}
                x={42.627}
                y={5.429}
                fill="#fff"
                rx={8}
            />
        </g>
        <path
            fill="#7E8BFF"
            d="m63.016 13.755-2.143 5.438q-.758 2.11-2.307 1.978-.462 0-.923-.132l-.132-1.253q.396.165.791.165.627.033.89-.494l.264-.626-2.208-5.076h1.78l1.021 3.065q.099.264.165.692h.033q.033-.165.099-.461a1 1 0 0 1 .066-.23l.923-3.066zM57.248 13.887l-.528 1.351a1.07 1.07 0 0 0-.659-.198q-1.055.033-1.088 1.319v2.636h-1.582v-5.24h1.582v.956q.396-1.088 1.385-1.088.527.033.89.264M52.634 13.03h-2.34v5.999h-1.682V13.03H46.24v-1.351h6.395z"
        />
        <path
            fill="#E371FF"
            d="M36.423 21.429c-2.432 1.492-4.196 3.376-4.992 4.32l-1.944-1.524-.86.692 3.356 3.416c.576-1.48 2.408-4.372 4.644-6.428z"
        />
        <defs>
            <filter
                id="step03_svg__a"
                width={67.084}
                height={63.636}
                x={0.585}
                y={2.78}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.177679 0 0 0 0 0 0 0 0 0 0.561091 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1085_2791"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1085_2791"
                    result="shape"
                />
            </filter>
            <filter
                id="step03_svg__d"
                width={35.581}
                height={28.002}
                x={38.827}
                y={46.779}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={3} />
                <feGaussianBlur stdDeviation={2.5} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0478431 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.15 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1085_2791"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1085_2791"
                    result="shape"
                />
            </filter>
            <filter
                id="step03_svg__f"
                width={32}
                height={28}
                x={38.627}
                y={2.429}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={1} />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.2 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1085_2791"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1085_2791"
                    result="shape"
                />
            </filter>
            <linearGradient
                id="step03_svg__b"
                x1={18.685}
                x2={-40.502}
                y1={-26.045}
                y2={13.238}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#64ABFF" />
                <stop offset={1} stopColor="#F585FF" />
            </linearGradient>
            <linearGradient
                id="step03_svg__c"
                x1={37.352}
                x2={94.156}
                y1={6.584}
                y2={33.861}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#64ABFF" />
                <stop offset={1} stopColor="#F585FF" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgStep03;
