import type { SVGProps } from "react";
const SvgStep01 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 73 73"
        {...props}
    >
        <g filter="url(#step01_svg__a)">
            <path
                fill="url(#step01_svg__b)"
                fillOpacity={0.9}
                d="M6.55 17.424a4 4 0 0 1 3.673-4.302l37.883-2.983a4 4 0 0 1 4.301 3.674l3.455 43.864a4 4 0 0 1-3.674 4.301l-37.883 2.983a4 4 0 0 1-4.301-3.673z"
            />
            <rect
                width={45.514}
                height={51.514}
                x={6.497}
                y={13.659}
                stroke="url(#step01_svg__c)"
                strokeWidth={0.486}
                rx={3.757}
                transform="rotate(-4.502 6.497 13.66)"
            />
            <rect
                width={37}
                height={43.343}
                x={11.599}
                y={17.857}
                fill="url(#step01_svg__d)"
                rx={2.114}
                transform="rotate(-4.502 11.6 17.857)"
            />
        </g>
        <g filter="url(#step01_svg__e)" transform="rotate(-4.502 17.963 9.503)">
            <rect
                width={23}
                height={7}
                x={17.963}
                y={9.503}
                fill="#fff"
                rx={3.5}
            />
            <rect
                width={23}
                height={7}
                x={17.963}
                y={9.503}
                fill="url(#step01_svg__f)"
                fillOpacity={0.4}
                rx={3.5}
            />
        </g>
        <rect
            width={12}
            height={12}
            x={17.147}
            y={24.614}
            fill="#54E3FF"
            fillOpacity={0.25}
            rx={4}
            transform="rotate(-4.502 17.147 24.614)"
        />
        <rect
            width={12}
            height={4}
            x={32.1}
            y={23.436}
            fill="#54E3FF"
            fillOpacity={0.25}
            rx={2}
            transform="rotate(-4.502 32.1 23.436)"
        />
        <rect
            width={12}
            height={4}
            x={32.65}
            y={30.415}
            fill="#54E3FF"
            fillOpacity={0.25}
            rx={2}
            transform="rotate(-4.502 32.65 30.415)"
        />
        <rect
            width={27}
            height={4}
            x={18.324}
            y={39.568}
            fill="#54E3FF"
            fillOpacity={0.25}
            rx={2}
            transform="rotate(-4.502 18.324 39.568)"
        />
        <rect
            width={27}
            height={4}
            x={18.795}
            y={46.549}
            fill="#54E3FF"
            fillOpacity={0.25}
            rx={2}
            transform="rotate(-4.502 18.795 46.55)"
        />
        <rect
            width={8}
            height={2}
            x={19.533}
            y={29.442}
            fill="#4FC8FF"
            rx={1}
            transform="rotate(-4.502 19.533 29.442)"
        />
        <rect
            width={8}
            height={2}
            x={24.282}
            y={26.058}
            fill="#4FC8FF"
            rx={1}
            transform="rotate(85.498 24.282 26.058)"
        />
        <g filter="url(#step01_svg__g)">
            <mask
                id="step01_svg__h"
                width={24}
                height={19}
                x={41.762}
                y={46.299}
                fill="#000"
                maskUnits="userSpaceOnUse"
            >
                <path fill="#fff" d="M41.762 46.299h24v19h-24z" />
                <path d="M48.811 62.508a15 15 0 0 1-1.507-.062 8 8 0 0 1-1.277-.252q-.711-.21-1.15-.586-.44-.397-.733-1.13-.315-.711-.314-1.716v-6.51q0-.962.293-1.695.292-.732.753-1.151.398-.355 1.089-.565a6 6 0 0 1 1.38-.293q.797-.042 1.466-.042.942 0 1.507.063.565.041 1.256.251.753.23 1.193.607t.753 1.088q.315.712.314 1.738v6.509q0 .963-.314 1.695-.292.733-.753 1.13-.42.357-1.151.586a5.6 5.6 0 0 1-1.36.273q-.588.062-1.445.062m.042-1.967q.88 0 1.256-.105.375-.125.502-.44.126-.313.126-1.025v-6.928q0-.711-.126-1.025-.126-.315-.502-.419-.377-.125-1.256-.125-.9 0-1.298.125-.376.105-.502.419-.126.313-.126 1.025v6.928q0 .712.126 1.026t.502.44q.398.104 1.298.104m11.534-8.623-3.705 2.05v-2.574l4.291-2.679h2.47V62.3h-3.056z" />
            </mask>
            <path
                fill="#4DBFFF"
                d="M48.811 62.508a15 15 0 0 1-1.507-.062 8 8 0 0 1-1.277-.252q-.711-.21-1.15-.586-.44-.397-.733-1.13-.315-.711-.314-1.716v-6.51q0-.962.293-1.695.292-.732.753-1.151.398-.355 1.089-.565a6 6 0 0 1 1.38-.293q.797-.042 1.466-.042.942 0 1.507.063.565.041 1.256.251.753.23 1.193.607t.753 1.088q.315.712.314 1.738v6.509q0 .963-.314 1.695-.292.733-.753 1.13-.42.357-1.151.586a5.6 5.6 0 0 1-1.36.273q-.588.062-1.445.062m.042-1.967q.88 0 1.256-.105.375-.125.502-.44.126-.313.126-1.025v-6.928q0-.711-.126-1.025-.126-.315-.502-.419-.377-.125-1.256-.125-.9 0-1.298.125-.376.105-.502.419-.126.313-.126 1.025v6.928q0 .712.126 1.026t.502.44q.398.104 1.298.104m11.534-8.623-3.705 2.05v-2.574l4.291-2.679h2.47V62.3h-3.056z"
            />
            <path
                fill="#fff"
                d="m47.304 62.446.213-1.989zm-1.277-.252-.564 1.92.019.005.02.005zm-1.15-.586-1.343 1.483.02.018.02.018zm-.733-1.13L46 59.735l-.013-.032-.015-.032zm-.021-9.92-1.857-.744zm.753-1.152-1.333-1.49-.006.005-.006.005zm1.089-.565.58 1.914.026-.008.026-.009zm1.38-.293-.104-1.997-.038.002-.038.003zm2.973.02-.22 1.989.036.004.036.002zm1.256.252.584-1.913-.004-.001zm1.946 1.695-1.83.808zm0 9.942-1.838-.788-.01.023-.009.022zm-.753 1.13 1.295 1.524.006-.005.006-.005zm-1.151.586-.6-1.907-.008.002-.008.003zm-1.36.273-.13-1.996-.042.002-.042.005zm-.147-2.01.535 1.927.049-.013.048-.016zm.502-.44 1.857.743zm0-8.978 1.857-.743zm-.502-.419-.633 1.897.049.017.048.013zm-2.554 0 .535 1.927.034-.01.033-.01zm-.502 9.398 1.857-.743zm.502.44-.632 1.897.06.02.063.016zm1.256 2.071v-2c-.578 0-1.002-.02-1.294-.051l-.213 1.989-.213 1.988c.49.053 1.07.074 1.72.074zm-1.507-.062.213-1.989a6 6 0 0 1-.963-.192l-.527 1.93-.526 1.929c.542.148 1.074.255 1.59.31zm-1.277-.252.565-1.918c-.277-.082-.387-.163-.414-.186l-1.302 1.518-1.301 1.519c.559.48 1.216.788 1.888.986zm-1.15-.586 1.341-1.483c-.005-.004-.1-.095-.217-.39l-1.857.743-1.857.743c.272.682.666 1.345 1.247 1.87zm-.733-1.13 1.83-.807c-.07-.158-.144-.44-.144-.91h-4c0 .87.135 1.733.484 2.524zm-.314-1.716h2v-6.51h-4v6.51zm0-6.51h2c0-.441.067-.746.15-.952l-1.857-.743-1.857-.743c-.308.77-.436 1.597-.436 2.439zm.293-1.695 1.857.743c.112-.28.21-.386.242-.414l-1.346-1.48-1.345-1.48c-.583.53-.987 1.192-1.265 1.888zm.753-1.151 1.334 1.49c-.015.014.06-.058.334-.141l-.58-1.914-.58-1.914c-.646.196-1.297.501-1.841.989zm1.089-.565.632 1.897q.48-.159.93-.198l-.181-1.992-.181-1.992a8 8 0 0 0-1.833.388zm1.38-.293.106 1.997q.752-.039 1.36-.039v-4q-.731 0-1.57.045zm1.466-.042v2c.595 0 1.015.02 1.286.05l.221-1.987.22-1.988a16 16 0 0 0-1.727-.075zm1.507.063-.148 1.994c.197.015.468.063.824.171l.58-1.914.58-1.914a8 8 0 0 0-1.688-.332zm1.256.251-.585 1.913c.329.1.454.193.476.213l1.302-1.519 1.301-1.519c-.563-.482-1.233-.794-1.91-1zm1.193.607-1.302 1.519c0-.001.024.02.066.08s.097.156.16.297l1.83-.808 1.83-.807c-.291-.659-.698-1.299-1.283-1.8zm.753 1.088-1.83.808c.068.152.144.44.144.93h4c0-.878-.133-1.749-.484-2.545zm.314 1.738h-2v6.509h4v-6.51zm0 6.509h-2c0 .426-.07.714-.152.907l1.838.788 1.839.788c.335-.783.475-1.625.475-2.483zm-.314 1.695-1.857-.743c-.11.277-.2.357-.203.36l1.307 1.513 1.307 1.514c.61-.527 1.023-1.2 1.303-1.901zm-.753 1.13-1.295-1.523c-.012.01-.128.099-.456.202l.6 1.907.6 1.909c.648-.204 1.3-.506 1.846-.97zm-1.151.586L51 60.271c-.34.11-.63.163-.873.179l.128 1.996.129 1.995a7.6 7.6 0 0 0 1.847-.365zm-1.36.273-.214-1.989c-.296.032-.701.051-1.231.051v4c.614 0 1.172-.022 1.657-.074zm-1.403-1.905v2c.599 0 1.256-.03 1.791-.178l-.535-1.927-.536-1.927c.046-.012.017 0-.133.013-.137.011-.33.019-.587.019zm1.256-.105.632 1.898c.702-.234 1.386-.742 1.727-1.595l-1.857-.742-1.857-.743a1.3 1.3 0 0 1 .352-.495c.15-.132.29-.194.37-.22zm.502-.44 1.857.743c.235-.587.269-1.258.269-1.768h-4c0 .184-.009.303-.017.37l-.004.03a1 1 0 0 1 .038-.117zm.126-1.025h2v-6.928h-4v6.928zm0-6.928h2c0-.51-.034-1.18-.269-1.768l-1.857.743-1.857.742a1 1 0 0 1-.038-.116l.004.03c.008.067.017.185.017.37zm-.126-1.025 1.857-.743c-.354-.884-1.078-1.396-1.824-1.603l-.535 1.927-.536 1.927a1.2 1.2 0 0 1-.43-.234c-.181-.15-.312-.339-.389-.532zm-.502-.419.632-1.897c-.582-.194-1.283-.228-1.888-.228v4c.25 0 .431.009.554.021.138.014.142.026.07.001zm-1.256-.125v-2c-.624 0-1.322.035-1.9.218l.602 1.907.602 1.907c-.053.017-.031.004.114-.01.133-.013.324-.023.582-.023zm-1.298.125-.535-1.927c-.746.207-1.47.719-1.824 1.603l1.857.743 1.857.742a1.35 1.35 0 0 1-.388.532 1.2 1.2 0 0 1-.431.234zm-.502.419-1.857-.743c-.235.587-.269 1.258-.269 1.768h4c0-.184.009-.302.017-.369l.004-.03a1 1 0 0 1-.038.117zm-.126 1.025h-2v6.928h4v-6.928zm0 6.928h-2c0 .51.034 1.181.269 1.769l1.857-.743 1.857-.743c.026.065.036.108.038.117l-.004-.03a3 3 0 0 1-.017-.37zm.126 1.026-1.857.742c.341.854 1.025 1.36 1.727 1.595l.632-1.898.633-1.897c.08.026.22.088.37.22.157.137.278.31.352.495zm.502.44-.509 1.933c.533.14 1.189.17 1.807.17v-4c-.265 0-.467-.007-.614-.019-.158-.013-.202-.026-.175-.019zm12.832-8.52h2v-3.393l-2.969 1.644zm-3.705 2.052h-2v3.393l2.97-1.643zm0-2.575-1.059-1.696-.94.587v1.11zm4.291-2.679v-2H60.4l-.486.304zm2.47 0h2v-2h-2zm0 13.584v2h2v-2zm-3.056 0h-2v2h2zm0-10.381-.969-1.75-3.704 2.051.968 1.75.97 1.75 3.704-2.052zm-3.705 2.05h2v-2.574h-4v2.575zm0-2.574 1.06 1.697 4.29-2.68-1.059-1.696-1.06-1.696-4.29 2.679zm4.291-2.679v2h2.47v-4h-2.47zm2.47 0h-2V62.3h4V48.715zm0 13.584v-2h-3.056v4h3.056zm-3.056 0h2V51.918h-4v10.381z"
                mask="url(#step01_svg__h)"
            />
        </g>
        <g filter="url(#step01_svg__i)">
            <rect
                width={24}
                height={24}
                x={44.408}
                y={6.366}
                fill="#fff"
                rx={1.5}
                transform="rotate(6 44.408 6.366)"
            />
        </g>
        <path
            fill="#4FC8FF"
            d="M61.976 16.968a.57.57 0 0 0-.333-.425l-5.05-3.52a.9.9 0 0 0-.81-.085l-5.734 2.386c-.263.103-.484.404-.518.726l-.608 5.784c-.034.32.12.662.355.817l5.05 3.52c.18.083.361.167.555.123.062.006.194-.045.256-.038l5.671-2.393a.84.84 0 0 0 .58-.719l.608-5.784c.014-.128-.035-.264-.022-.392m-6.39 2.642-.575 5.463-.138.115-4.814-3.365.58-5.527 5.41-2.29 4.814 3.365z"
        />
        <rect
            width={2.25}
            height={2.25}
            x={45.743}
            y={8.015}
            fill="#4FC8FF"
            rx={0.75}
            transform="rotate(6 45.743 8.015)"
        />
        <rect
            width={2.25}
            height={2.25}
            x={64.391}
            y={9.975}
            fill="#4FC8FF"
            rx={0.75}
            transform="rotate(6 64.39 9.975)"
        />
        <rect
            width={2.25}
            height={2.25}
            x={62.43}
            y={28.622}
            fill="#4FC8FF"
            rx={0.75}
            transform="rotate(6 62.43 28.622)"
        />
        <rect
            width={2.25}
            height={2.25}
            x={43.783}
            y={26.662}
            fill="#4FC8FF"
            rx={0.75}
            transform="rotate(6 43.783 26.662)"
        />
        <rect
            width={0.75}
            height={15}
            x={64.823}
            y={13.037}
            fill="#4FC8FF"
            rx={0.375}
            transform="rotate(6 64.823 13.037)"
        />
        <rect
            width={0.75}
            height={15}
            x={46.61}
            y={28.467}
            fill="#4FC8FF"
            rx={0.375}
            transform="rotate(-84 46.61 28.467)"
        />
        <rect
            width={0.75}
            height={15}
            x={48.57}
            y={9.82}
            fill="#4FC8FF"
            rx={0.375}
            transform="rotate(-84 48.57 9.82)"
        />
        <rect
            width={0.75}
            height={15}
            x={46.175}
            y={11.077}
            fill="#4FC8FF"
            rx={0.375}
            transform="rotate(6 46.175 11.077)"
        />
        <path
            stroke="#4FC8FF"
            strokeDasharray="2 1"
            d="M26 35.82c-.166 1.667 7.665 8 14.5 8 7 0 13.5-1.5 17.5-12.5l-2.5 1.5"
        />
        <defs>
            <linearGradient
                id="step01_svg__b"
                x1={10.5}
                x2={49.5}
                y1={-0.445}
                y2={61.555}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#359DFF" />
                <stop offset={1} stopColor="#46F9FF" />
            </linearGradient>
            <linearGradient
                id="step01_svg__c"
                x1={9.167}
                x2={45.21}
                y1={3.729}
                y2={7.91}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.8} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="step01_svg__d"
                x1={30.099}
                x2={30.099}
                y1={17.857}
                y2={61.2}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" />
                <stop offset={1} stopColor="#E9FCFF" />
            </linearGradient>
            <linearGradient
                id="step01_svg__f"
                x1={29.463}
                x2={29.463}
                y1={9.503}
                y2={16.503}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#87CFFF" />
                <stop offset={0.475} stopColor="#D5EBFF" />
                <stop offset={0.755} stopColor="#96C4FF" />
                <stop offset={1} stopColor="#fff" />
            </linearGradient>
            <filter
                id="step01_svg__a"
                width={59.067}
                height={64.577}
                x={1.672}
                y={5.262}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={2.432} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.193025 0 0 0 0 0.482562 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4463"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4463"
                    result="shape"
                />
            </filter>
            <filter
                id="step01_svg__e"
                width={30.951}
                height={16.256}
                x={14.227}
                y={4.961}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={1} />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.290706 0 0 0 0 0.792835 0 0 0 0.36 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4463"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4463"
                    result="shape"
                />
            </filter>
            <filter
                id="step01_svg__g"
                width={33.613}
                height={28.002}
                x={36.83}
                y={44.506}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={3} />
                <feGaussianBlur stdDeviation={2.5} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.15 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4463"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4463"
                    result="shape"
                />
            </filter>
            <filter
                id="step01_svg__i"
                width={34.081}
                height={34.081}
                x={38.048}
                y={3.515}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={1} />
                <feGaussianBlur stdDeviation={2} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0.2 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_1075_4463"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_1075_4463"
                    result="shape"
                />
            </filter>
        </defs>
    </svg>
);
export default SvgStep01;
